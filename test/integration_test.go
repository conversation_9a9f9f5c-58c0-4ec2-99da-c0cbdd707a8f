package test

import (
	"fmt"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"yourdomain.com/live-streaming/internal/hls"
	"yourdomain.com/live-streaming/internal/rtmp"
)

func TestFullStreamingPipeline(t *testing.T) {
	if !isFFmpegAvailable() {
		t.Skip("FFmpeg not available, skipping integration test")
	}

	// Create temporary directory for HLS files
	tempDir := t.TempDir()
	
	// Get available ports
	rtmpPort := getAvailablePort(t)
	httpPort := getAvailablePort(t)
	
	// Create HLS manager
	hlsManager := hls.NewManager(tempDir)
	
	// Create RTMP server
	rtmpServer := rtmp.NewServer(rtmpPort, hlsManager)
	
	// Start RTMP server
	go func() {
		if err := rtmpServer.Start(); err != nil {
			t.Logf("RTMP server error: %v", err)
		}
	}()
	defer rtmpServer.Stop()
	
	// Give server time to start
	time.Sleep(200 * time.Millisecond)
	
	// Test 1: Verify RTMP server is listening
	t.Run("RTMP Server Listening", func(t *testing.T) {
		conn, err := net.Dial("tcp", fmt.Sprintf("localhost:%d", rtmpPort))
		if err != nil {
			t.Fatalf("RTMP server not listening: %v", err)
		}
		conn.Close()
	})
	
	// Test 2: Stream with FFmpeg and verify HLS files are created
	t.Run("Stream and HLS Generation", func(t *testing.T) {
		streamKey := "test-stream"
		rtmpURL := fmt.Sprintf("rtmp://localhost:%d/live/%s", rtmpPort, streamKey)
		
		// Start streaming in background
		cmd := exec.Command("ffmpeg",
			"-f", "lavfi",
			"-i", "testsrc=duration=5:size=320x240:rate=30",
			"-f", "lavfi",
			"-i", "sine=frequency=1000:duration=5",
			"-c:v", "libx264",
			"-preset", "ultrafast",
			"-c:a", "aac",
			"-f", "flv",
			rtmpURL,
		)
		
		// Start the command but don't wait for it to finish
		err := cmd.Start()
		if err != nil {
			t.Fatalf("Failed to start FFmpeg: %v", err)
		}
		
		// Wait for HLS files to be created
		expectedDir := filepath.Join(tempDir, "live", streamKey)
		playlistPath := filepath.Join(expectedDir, "stream.m3u8")
		
		// Wait up to 10 seconds for files to appear
		var playlistExists bool
		for i := 0; i < 20; i++ {
			if _, err := os.Stat(playlistPath); err == nil {
				playlistExists = true
				break
			}
			time.Sleep(500 * time.Millisecond)
		}
		
		// Kill the FFmpeg process
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		
		if !playlistExists {
			t.Errorf("HLS playlist not created at %s", playlistPath)
		}
		
		// Check if directory was created
		if _, err := os.Stat(expectedDir); os.IsNotExist(err) {
			t.Errorf("Stream directory not created: %s", expectedDir)
		}
	})
	
	// Test 3: HTTP server for playlist access
	t.Run("HTTP Playlist Access", func(t *testing.T) {
		// Start a simple HTTP server for testing
		mux := http.NewServeMux()
		mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
			streamKey := strings.TrimSuffix(filepath.Base(r.URL.Path), ".m3u8")
			if streamKey == "" {
				http.Error(w, "Invalid stream URL", http.StatusBadRequest)
				return
			}
			
			// For testing, just return a success response
			w.Header().Set("Content-Type", "application/vnd.apple.mpegurl")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("#EXTM3U\n#EXT-X-VERSION:3\n"))
		})
		
		server := &http.Server{
			Addr:    fmt.Sprintf(":%d", httpPort),
			Handler: mux,
		}
		
		go func() {
			server.ListenAndServe()
		}()
		defer server.Close()
		
		// Give HTTP server time to start
		time.Sleep(100 * time.Millisecond)
		
		// Test playlist request
		resp, err := http.Get(fmt.Sprintf("http://localhost:%d/test-stream.m3u8", httpPort))
		if err != nil {
			t.Fatalf("Failed to request playlist: %v", err)
		}
		defer resp.Body.Close()
		
		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
		
		contentType := resp.Header.Get("Content-Type")
		if contentType != "application/vnd.apple.mpegurl" {
			t.Errorf("Expected content type application/vnd.apple.mpegurl, got %s", contentType)
		}
	})
}

func TestRTMPServerStress(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping stress test in short mode")
	}
	
	tempDir := t.TempDir()
	rtmpPort := getAvailablePort(t)
	
	hlsManager := hls.NewManager(tempDir)
	rtmpServer := rtmp.NewServer(rtmpPort, hlsManager)
	
	go func() {
		rtmpServer.Start()
	}()
	defer rtmpServer.Stop()
	
	time.Sleep(200 * time.Millisecond)
	
	// Test multiple concurrent connections
	t.Run("Multiple Connections", func(t *testing.T) {
		const numConnections = 10
		errChan := make(chan error, numConnections)
		
		for i := 0; i < numConnections; i++ {
			go func(id int) {
				conn, err := net.Dial("tcp", fmt.Sprintf("localhost:%d", rtmpPort))
				if err != nil {
					errChan <- fmt.Errorf("connection %d failed: %v", id, err)
					return
				}
				
				// Hold connection briefly
				time.Sleep(100 * time.Millisecond)
				conn.Close()
				errChan <- nil
			}(i)
		}
		
		// Wait for all connections
		for i := 0; i < numConnections; i++ {
			if err := <-errChan; err != nil {
				t.Error(err)
			}
		}
	})
}

func TestStreamKeyValidation(t *testing.T) {
	tests := []struct {
		name      string
		path      string
		expected  string
		shouldErr bool
	}{
		{"Valid stream key", "/live/test-stream", "live/test-stream", false},
		{"Valid simple key", "/mystream", "mystream", false},
		{"Empty path", "/", "", true},
		{"Root only", "", "", true},
		{"Complex path", "/live/user123/stream", "live/user123/stream", false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			streamKey := strings.TrimPrefix(tt.path, "/")
			
			if tt.shouldErr && streamKey != "" {
				// For empty results, we expect them to be caught by validation
				if streamKey != tt.expected {
					t.Errorf("Expected %s, got %s", tt.expected, streamKey)
				}
			} else if !tt.shouldErr {
				if streamKey != tt.expected {
					t.Errorf("Expected %s, got %s", tt.expected, streamKey)
				}
			}
		})
	}
}

// Helper functions

func getAvailablePort(t *testing.T) int {
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		t.Fatalf("Could not get available port: %v", err)
	}
	port := listener.Addr().(*net.TCPAddr).Port
	listener.Close()
	return port
}

func isFFmpegAvailable() bool {
	_, err := exec.LookPath("ffmpeg")
	return err == nil
}

func waitForFile(path string, timeout time.Duration) bool {
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		if _, err := os.Stat(path); err == nil {
			return true
		}
		time.Sleep(100 * time.Millisecond)
	}
	return false
}
