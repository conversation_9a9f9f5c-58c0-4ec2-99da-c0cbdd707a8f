package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"yourdomain.com/live-streaming/internal/bunnycdn"
	"yourdomain.com/live-streaming/internal/config"
	"yourdomain.com/live-streaming/internal/hls"
	"yourdomain.com/live-streaming/internal/rtmp"
	"yourdomain.com/live-streaming/internal/watcher"
)

func main() {
	cfg := config.Load()

	// Create HLS manager
	hlsManager := hls.NewManager(cfg.HLSDir)

	// Create RTMP server
	rtmpServer := rtmp.NewServer(cfg.RTMPPort, hlsManager)
	go func() {
		if err := rtmpServer.Start(); err != nil {
			log.Printf("RTMP server error: %v", err)
		}
	}()
	defer rtmpServer.Stop()
	log.Printf("RTMP server listening on :%d", cfg.RTMPPort)

	// Create Bunny CDN uploader
	uploader := bunnycdn.NewUploader(
		cfg.BunnyCDNEndpoint,
		cfg.BunnyCDNUsername,
		cfg.BunnyCDNPassword,
		cfg.BunnyCDNStoragePath,
	)

	// Create file watcher
	fileWatcher := watcher.NewFileWatcher(cfg.HLSDir, uploader)
	go fileWatcher.Start()
	defer fileWatcher.Stop()
	log.Printf("Watching HLS directory: %s", cfg.HLSDir)

	// Start HTTP server
	httpServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.HTTPPort),
		Handler: http.HandlerFunc(handlePlaylistRequest(cfg)),
	}
	go func() {
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP server error: %v", err)
		}
	}()
	log.Printf("HTTP server listening on :%d", cfg.HTTPPort)

	// Wait for shutdown signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
	log.Println("Shutting down...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := httpServer.Shutdown(ctx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	}
}

func handlePlaylistRequest(cfg *config.Config) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		// Extract stream key from URL path
		streamKey := strings.TrimSuffix(filepath.Base(r.URL.Path), ".m3u8")
		if streamKey == "" {
			http.Error(w, "Invalid stream URL", http.StatusBadRequest)
			return
		}

		// Construct CDN URL for playlist
		cdnURL := fmt.Sprintf("%s/%s/stream.m3u8",
			cfg.BunnyCDNURL,
			streamKey,
		)

		// Redirect to CDN URL
		http.Redirect(w, r, cdnURL, http.StatusFound)
	}
}
