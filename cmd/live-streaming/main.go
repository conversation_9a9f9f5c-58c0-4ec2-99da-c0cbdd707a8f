package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"yourdomain.com/live-streaming/internal/bunnycdn"
	"yourdomain.com/live-streaming/internal/config"
	"yourdomain.com/live-streaming/internal/hls"
	"yourdomain.com/live-streaming/internal/rtmp"
	"yourdomain.com/live-streaming/internal/watcher"
)

func main() {
	cfg := config.Load()

	// Create HLS manager
	hlsManager := hls.NewManager(cfg.HLSDir)

	// Create RTMP server
	rtmpServer := rtmp.NewServer(cfg.RTMPPort, hlsManager)
	go func() {
		if err := rtmpServer.Start(); err != nil {
			log.Printf("RTMP server error: %v", err)
		}
	}()
	defer rtmpServer.Stop()
	log.Printf("RTMP server listening on :%d", cfg.RTMPPort)

	// Create Bunny CDN uploader
	uploader := bunnycdn.NewUploader(
		cfg.BunnyCDNEndpoint,
		cfg.BunnyCDNUsername,
		cfg.BunnyCDNPassword,
		cfg.BunnyCDNStoragePath,
	)

	// Create file watcher
	fileWatcher := watcher.NewFileWatcher(cfg.HLSDir, uploader)
	go fileWatcher.Start()
	defer fileWatcher.Stop()
	log.Printf("Watching HLS directory: %s", cfg.HLSDir)

	// Start HTTP server with router
	mux := http.NewServeMux()
	mux.HandleFunc("/", handlePlaylistRequest(cfg))
	mux.HandleFunc("/stream/", handlePlayerRequest(cfg))

	// Serve HLS files statically
	hlsFileServer := http.FileServer(http.Dir(cfg.HLSDir))
	mux.Handle("/hls/", http.StripPrefix("/hls/", hlsFileServer))

	httpServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.HTTPPort),
		Handler: mux,
	}
	go func() {
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP server error: %v", err)
		}
	}()
	log.Printf("HTTP server listening on :%d", cfg.HTTPPort)

	// Wait for shutdown signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
	log.Println("Shutting down...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := httpServer.Shutdown(ctx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	}
}

func handlePlaylistRequest(cfg *config.Config) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		// Extract stream key from URL path
		streamKey := strings.TrimSuffix(filepath.Base(r.URL.Path), ".m3u8")
		if streamKey == "" {
			http.Error(w, "Invalid stream URL", http.StatusBadRequest)
			return
		}

		// Construct CDN URL for playlist
		cdnURL := fmt.Sprintf("%s/%s/stream.m3u8",
			cfg.BunnyCDNURL,
			streamKey,
		)

		// Redirect to CDN URL
		http.Redirect(w, r, cdnURL, http.StatusFound)
	}
}

func handlePlayerRequest(cfg *config.Config) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		// Extract stream ID from URL path: /stream/<stream_id>
		path := strings.TrimPrefix(r.URL.Path, "/stream/")
		streamID := strings.Trim(path, "/")

		if streamID == "" {
			http.Error(w, "Stream ID is required", http.StatusBadRequest)
			return
		}

		// Check if stream exists locally first
		localPlaylistPath := filepath.Join(cfg.HLSDir, streamID, "stream.m3u8")
		var playlistURL string

		if _, err := os.Stat(localPlaylistPath); err == nil {
			// Stream exists locally, use local URL
			playlistURL = fmt.Sprintf("http://localhost:%d/hls/%s/stream.m3u8", cfg.HTTPPort, streamID)
		} else {
			// Use CDN URL
			playlistURL = fmt.Sprintf("%s/%s/stream.m3u8", cfg.BunnyCDNURL, streamID)
		}

		// Serve HTML player page
		w.Header().Set("Content-Type", "text/html")
		playerHTML := generatePlayerHTML(streamID, playlistURL)
		w.Write([]byte(playerHTML))
	}
}

func generatePlayerHTML(streamID, playlistURL string) string {
	return fmt.Sprintf(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stream Player - %s</title>
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .video-container {
            position: relative;
            width: 100%%;
            max-width: 800px;
            margin: 0 auto;
        }
        .video-js {
            width: 100%%;
            height: auto;
        }
        .stream-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .stream-info h3 {
            margin-top: 0;
            color: #495057;
        }
        .stream-info p {
            margin: 5px 0;
            color: #6c757d;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.live {
            background-color: #dc3545;
            color: white;
        }
        .status.offline {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Live Stream Player</h1>

        <div class="video-container">
            <video-js
                id="stream-player"
                class="video-js vjs-default-skin"
                controls
                preload="auto"
                width="800"
                height="450"
                data-setup="{}">
                <p class="vjs-no-js">
                    To view this video please enable JavaScript, and consider upgrading to a web browser that
                    <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>.
                </p>
            </video-js>
        </div>

        <div class="stream-info">
            <h3>Stream Information</h3>
            <p><strong>Stream ID:</strong> %s</p>
            <p><strong>Status:</strong> <span id="stream-status" class="status offline">Checking...</span></p>
            <p><strong>Playlist URL:</strong> <a href="%s" target="_blank">%s</a></p>
            <p><strong>Format:</strong> HLS (HTTP Live Streaming)</p>
        </div>
    </div>

    <script>
        // Initialize Video.js player with ultra-low latency configuration
        var player = videojs('stream-player', {
            sources: [{
                src: '%s',
                type: 'application/x-mpegURL'
            }],
            fluid: true,
            responsive: true,
            liveui: true,
            liveTracker: {
                trackingThreshold: 0,
                liveTolerance: 0.5
            },
            html5: {
                hls: {
                    enableLowInitialPlaylist: true,
                    smoothQualityChange: false,
                    overrideNative: true,
                    // Ultra-low latency settings
                    liveSyncDurationCount: 1,        // Only keep 1 segment in buffer
                    liveMaxLatencyDurationCount: 2,  // Max 2 segments latency
                    maxBufferLength: 1,              // 1 second max buffer
                    maxBufferSize: 1024 * 1024,      // 1MB max buffer size
                    maxBufferHole: 0.1,              // Small buffer holes
                    highBufferWatchdogPeriod: 1,     // Check buffer every 1 second
                    nudgeOffset: 0.1,                // Small nudge offset
                    nudgeMaxRetry: 3,                // Limited retries
                    maxLoadingDelay: 1,              // Fast loading
                    manifestLoadingTimeOut: 2000,    // 2 second timeout
                    manifestLoadingMaxRetry: 1,      // Single retry
                    levelLoadingTimeOut: 2000,       // 2 second timeout
                    levelLoadingMaxRetry: 1,         // Single retry
                    fragLoadingTimeOut: 2000,        // 2 second timeout
                    fragLoadingMaxRetry: 1           // Single retry
                }
            }
        });

        // Handle player events
        player.ready(function() {
            console.log('Player is ready');

            // Enable low-latency mode
            player.liveTracker.seekToLiveEdge();

            // Set playback rate slightly faster to catch up
            player.playbackRate(1.0);

            checkStreamStatus();
        });

        player.on('loadstart', function() {
            console.log('Loading stream...');
            updateStatus('Connecting...', 'offline');
        });

        player.on('canplay', function() {
            console.log('Stream can play');
            updateStatus('Live', 'live');

            // Seek to live edge for minimal latency
            if (player.liveTracker) {
                player.liveTracker.seekToLiveEdge();
            }
        });

        player.on('error', function(e) {
            console.error('Player error:', e);
            updateStatus('Offline', 'offline');
        });

        // Monitor latency and adjust playback
        player.on('timeupdate', function() {
            if (player.liveTracker && player.liveTracker.isLive()) {
                var latency = player.liveTracker.liveCurrentTime() - player.currentTime();

                // If latency is too high, seek to live edge
                if (latency > 2) {
                    console.log('High latency detected (' + latency.toFixed(2) + 's), seeking to live edge');
                    player.liveTracker.seekToLiveEdge();
                }
            }
        });

        // Function to update stream status
        function updateStatus(text, className) {
            var statusElement = document.getElementById('stream-status');
            statusElement.textContent = text;
            statusElement.className = 'status ' + className;
        }

        // Function to check if stream is available
        function checkStreamStatus() {
            fetch('%s')
                .then(response => {
                    if (response.ok) {
                        updateStatus('Live', 'live');
                    } else {
                        updateStatus('Offline', 'offline');
                    }
                })
                .catch(error => {
                    console.error('Error checking stream status:', error);
                    updateStatus('Offline', 'offline');
                });
        }

        // Periodically check stream status
        setInterval(checkStreamStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>`, streamID, streamID, playlistURL, playlistURL, playlistURL, playlistURL)
}
