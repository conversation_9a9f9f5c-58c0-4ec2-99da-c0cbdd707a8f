{"conf_version": "v0.4.1", "rtmp": {"enable": true, "addr": ":1935"}, "default_http": {"http_listen_addr": ":0"}, "hls": {"enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 500, "fragment_num": 3, "delete_threshold": 3, "cleanup_mode": 1, "use_memory_as_disk_flag": false}, "http_api": {"enable": false, "addr": ":0"}, "log": {"level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1}}