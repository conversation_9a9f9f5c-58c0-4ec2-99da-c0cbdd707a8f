#!/bin/bash

# Test runner script for the RTMP streaming server
# Runs both unit tests and integration tests

set -e

echo "🧪 Running RTMP Streaming Server Tests"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Function to run tests with timeout
run_with_timeout() {
    local timeout=$1
    shift
    timeout $timeout "$@"
}

echo "1. Running unit tests..."
print_header "=== Unit Tests ==="

# Run unit tests for RTMP package
if go test -v ./internal/rtmp/... -timeout=30s; then
    print_status "RTMP unit tests passed"
else
    print_error "RTMP unit tests failed"
    exit 1
fi

# Run unit tests for HLS package
if go test -v ./internal/hls/... -timeout=30s; then
    print_status "HLS unit tests passed"
else
    print_warning "HLS unit tests failed or package doesn't exist"
fi

echo ""
echo "2. Running integration tests..."
print_header "=== Integration Tests ==="

# Check if FFmpeg is available for integration tests
if ! command -v ffmpeg >/dev/null 2>&1; then
    print_warning "FFmpeg not found. Skipping integration tests that require FFmpeg."
    echo "To install FFmpeg:"
    echo "  Ubuntu/Debian: sudo apt install ffmpeg"
    echo "  macOS: brew install ffmpeg"
    echo "  Windows: Download from https://ffmpeg.org/"
else
    print_status "FFmpeg is available"
    
    # Run integration tests
    if go test -v ./test/... -timeout=60s; then
        print_status "Integration tests passed"
    else
        print_error "Integration tests failed"
        exit 1
    fi
fi

echo ""
echo "3. Running end-to-end test..."
print_header "=== End-to-End Test ==="

# Check if the test script exists and is executable
if [ -x "./scripts/test_streaming.sh" ]; then
    print_status "Running end-to-end streaming test..."
    
    # Run the streaming test with timeout
    if run_with_timeout 120s ./scripts/test_streaming.sh; then
        print_status "End-to-end test passed"
    else
        print_error "End-to-end test failed"
        exit 1
    fi
else
    print_error "End-to-end test script not found or not executable"
    exit 1
fi

echo ""
echo "4. Running performance tests..."
print_header "=== Performance Tests ==="

# Run benchmark tests
if go test -bench=. -benchmem ./internal/rtmp/... -timeout=30s; then
    print_status "Performance tests completed"
else
    print_warning "Performance tests failed or not available"
fi

echo ""
print_header "=== Test Summary ==="
print_status "Unit tests: PASSED"
if command -v ffmpeg >/dev/null 2>&1; then
    print_status "Integration tests: PASSED"
else
    print_warning "Integration tests: SKIPPED (FFmpeg not available)"
fi
print_status "End-to-end test: PASSED"
print_status "Performance tests: COMPLETED"

echo ""
echo "🎉 All tests completed successfully!"
echo ""
echo "Your RTMP streaming server is working correctly and ready for use."
echo ""
echo "Next steps:"
echo "  1. Start the server: ./streamer"
echo "  2. Stream with OBS to: rtmp://localhost:1935/live/your-stream-key"
echo "  3. Watch at: http://localhost:8080/live/your-stream-key.m3u8"
