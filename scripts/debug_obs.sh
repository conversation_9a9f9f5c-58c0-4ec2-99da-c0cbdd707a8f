#!/bin/bash

# Debug script for OBS connection issues
# This script helps diagnose why OBS might not be connecting

echo "🔍 OBS Connection Debugging"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_info() {
    echo -e "${YELLOW}ℹ${NC} $1"
}

# Function to check if port is listening
check_port() {
    local port=$1
    if nc -z localhost $port 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to test RTMP handshake
test_rtmp_handshake() {
    local port=$1
    echo "Testing RTMP handshake on port $port..."
    
    # Use FFmpeg to test RTMP connection
    timeout 10s ffmpeg -f lavfi -i testsrc=duration=2:size=320x240:rate=30 \
                      -f lavfi -i sine=frequency=1000:duration=2 \
                      -c:v libx264 -preset ultrafast \
                      -c:a aac -f flv \
                      rtmp://localhost:$port/live/debug-test \
                      -y /dev/null 2>&1
    
    local exit_code=$?
    if [ $exit_code -eq 0 ] || [ $exit_code -eq 124 ]; then
        # Exit code 124 is timeout, which means connection worked
        return 0
    else
        return 1
    fi
}

echo ""
print_header "=== 1. Basic Connectivity Tests ==="

# Check if server is running
if check_port 1935; then
    print_status "RTMP server is listening on port 1935"
else
    print_error "RTMP server is NOT listening on port 1935"
    echo ""
    print_info "Make sure your server is running with: ./streamer"
    exit 1
fi

if check_port 8080; then
    print_status "HTTP server is listening on port 8080"
else
    print_warning "HTTP server is NOT listening on port 8080"
fi

echo ""
print_header "=== 2. Network Interface Check ==="

# Check what interfaces the server is binding to
echo "Checking network bindings..."
netstat -tlnp 2>/dev/null | grep ":1935" || ss -tlnp 2>/dev/null | grep ":1935" || print_warning "Could not check network bindings"

echo ""
print_header "=== 3. Firewall Check ==="

# Check if firewall might be blocking
if command -v ufw >/dev/null 2>&1; then
    echo "UFW status:"
    sudo ufw status 2>/dev/null || print_warning "Could not check UFW status"
elif command -v firewall-cmd >/dev/null 2>&1; then
    echo "Firewalld status:"
    sudo firewall-cmd --list-ports 2>/dev/null || print_warning "Could not check firewalld status"
else
    print_info "No common firewall tools found"
fi

echo ""
print_header "=== 4. RTMP Protocol Test ==="

if command -v ffmpeg >/dev/null 2>&1; then
    print_info "Testing RTMP handshake with FFmpeg..."
    if test_rtmp_handshake 1935; then
        print_status "RTMP handshake successful"
    else
        print_error "RTMP handshake failed"
        echo ""
        print_info "This suggests an issue with RTMP protocol handling"
    fi
else
    print_warning "FFmpeg not available for RTMP testing"
fi

echo ""
print_header "=== 5. Server Process Check ==="

# Check if our server process is running
if pgrep -f "./streamer" >/dev/null; then
    print_status "Streamer process is running"
    echo "Process details:"
    ps aux | grep "./streamer" | grep -v grep
else
    print_error "Streamer process is NOT running"
fi

echo ""
print_header "=== 6. OBS Configuration Check ==="

echo "For OBS Studio, use these settings:"
echo ""
echo "Stream Settings:"
echo "  Service: Custom"
echo "  Server: rtmp://localhost:1935/live"
echo "  Stream Key: your-stream-name"
echo ""
echo "Advanced Settings (if needed):"
echo "  Keyframe Interval: 2"
echo "  CPU Usage Preset: ultrafast"
echo "  Profile: baseline"
echo ""

echo ""
print_header "=== 7. Common Issues & Solutions ==="

echo "If OBS still won't connect, try:"
echo ""
echo "1. Check OBS logs:"
echo "   Help → Log Files → Current Log File"
echo "   Look for connection errors"
echo ""
echo "2. Try different stream key:"
echo "   Use simple names like 'test' or 'stream1'"
echo ""
echo "3. Restart OBS:"
echo "   Sometimes OBS needs a restart after changing settings"
echo ""
echo "4. Check server logs:"
echo "   Look at the terminal where you started ./streamer"
echo "   You should see 'New stream: live/your-stream-key'"
echo ""
echo "5. Test with different software:"
echo "   Try streaming with FFmpeg first:"
echo "   ffmpeg -f lavfi -i testsrc=duration=10:size=640x480:rate=30 \\"
echo "          -f lavfi -i sine=frequency=1000:duration=10 \\"
echo "          -c:v libx264 -c:a aac -f flv \\"
echo "          rtmp://localhost:1935/live/test-stream"
echo ""

echo ""
print_header "=== 8. Real-time Monitoring ==="

echo "To monitor connections in real-time, run:"
echo "  watch -n 1 'netstat -an | grep :1935'"
echo ""
echo "To see server logs with timestamps:"
echo "  ./streamer 2>&1 | while read line; do echo \"[\$(date)] \$line\"; done"
echo ""

print_info "If the issue persists, please share:"
print_info "1. OBS log file contents"
print_info "2. Server terminal output"
print_info "3. Results of this debug script"
