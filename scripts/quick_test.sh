#!/bin/bash

# Quick test script for RTMP streaming server
# This script performs a fast verification that the server is working

set -e

echo "🚀 Quick RTMP Server Test"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Function to check if port is available
port_available() {
    ! nc -z localhost $1 2>/dev/null
}

# Function to wait for port to be available
wait_for_port() {
    local port=$1
    local timeout=${2:-5}
    local count=0
    
    while ! nc -z localhost $port 2>/dev/null; do
        if [ $count -ge $timeout ]; then
            return 1
        fi
        sleep 1
        ((count++))
    done
    return 0
}

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up..."
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
    fi
    pkill -f "./streamer" 2>/dev/null || true
}

trap cleanup EXIT

echo "1. Building the server..."
if ! go build -o streamer ./cmd/live-streaming; then
    print_error "Failed to build server"
    exit 1
fi
print_status "Server built successfully"

echo "2. Checking ports..."
if ! port_available 1935; then
    print_error "Port 1935 is already in use"
    exit 1
fi

if ! port_available 8080; then
    print_error "Port 8080 is already in use"
    exit 1
fi
print_status "Ports are available"

echo "3. Starting server..."
./streamer > /dev/null 2>&1 &
SERVER_PID=$!

if ! wait_for_port 1935 10; then
    print_error "RTMP server failed to start"
    exit 1
fi
print_status "RTMP server started on port 1935"

if ! wait_for_port 8080 10; then
    print_error "HTTP server failed to start"
    exit 1
fi
print_status "HTTP server started on port 8080"

echo "4. Testing connections..."
if command -v nc >/dev/null 2>&1; then
    if echo "" | nc -w 1 localhost 1935; then
        print_status "RTMP port accepts connections"
    else
        print_error "RTMP port not accepting connections"
        exit 1
    fi
    
    if echo "" | nc -w 1 localhost 8080; then
        print_status "HTTP port accepts connections"
    else
        print_error "HTTP port not accepting connections"
        exit 1
    fi
else
    print_warning "netcat not available, skipping connection tests"
fi

echo "5. Testing with FFmpeg (if available)..."
if command -v ffmpeg >/dev/null 2>&1; then
    # Create a very short test stream
    timeout 10s ffmpeg -f lavfi -i testsrc=duration=2:size=320x240:rate=30 \
                      -f lavfi -i sine=frequency=1000:duration=2 \
                      -c:v libx264 -preset ultrafast \
                      -c:a aac -f flv \
                      rtmp://localhost:1935/live/test-stream \
                      > /dev/null 2>&1 &
    FFMPEG_PID=$!
    
    # Wait a moment for processing
    sleep 3
    
    # Check if HLS directory was created
    if [ -d "./hls/live/test-stream" ]; then
        print_status "HLS directory created"
        
        # Check if any files were created
        FILE_COUNT=$(find ./hls/live/test-stream -name "*.ts" -o -name "*.m3u8" | wc -l)
        if [ $FILE_COUNT -gt 0 ]; then
            print_status "HLS files generated ($FILE_COUNT files)"
        else
            print_warning "HLS directory created but no files found yet"
        fi
    else
        print_warning "HLS directory not created (may need more time)"
    fi
    
    # Kill FFmpeg if still running
    kill $FFMPEG_PID 2>/dev/null || true
    wait $FFMPEG_PID 2>/dev/null || true
else
    print_warning "FFmpeg not available, skipping streaming test"
fi

echo ""
echo "🎉 Quick Test Results:"
echo "====================="
print_status "Server builds successfully"
print_status "RTMP server starts and accepts connections"
print_status "HTTP server starts and accepts connections"

if command -v ffmpeg >/dev/null 2>&1; then
    print_status "FFmpeg streaming test completed"
else
    print_warning "FFmpeg test skipped (not installed)"
fi

echo ""
echo "✅ Your RTMP streaming server is working!"
echo ""
echo "To use it:"
echo "  1. Start: ./streamer"
echo "  2. Stream to: rtmp://localhost:1935/live/your-stream-key"
echo "  3. Watch at: http://localhost:8080/live/your-stream-key.m3u8"
