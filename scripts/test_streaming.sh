#!/bin/bash

# Test script for RTMP streaming server
# This script tests the complete streaming pipeline

set -e

echo "🧪 Testing RTMP Streaming Server"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test configuration
RTMP_PORT=1935
HTTP_PORT=8080
STREAM_KEY="test-stream"
TEST_DURATION=5

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost $1 2>/dev/null
}

# Function to wait for port to be available
wait_for_port() {
    local port=$1
    local timeout=${2:-10}
    local count=0
    
    while ! nc -z localhost $port 2>/dev/null; do
        if [ $count -ge $timeout ]; then
            return 1
        fi
        sleep 1
        ((count++))
    done
    return 0
}

# Function to cleanup background processes
cleanup() {
    echo "🧹 Cleaning up..."
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
    fi
    if [ ! -z "$FFMPEG_PID" ]; then
        kill $FFMPEG_PID 2>/dev/null || true
    fi
    # Clean up any remaining processes
    pkill -f "./streamer" 2>/dev/null || true
    pkill -f "ffmpeg.*rtmp://localhost:$RTMP_PORT" 2>/dev/null || true
}

# Set up cleanup trap
trap cleanup EXIT

echo "1. Checking prerequisites..."

# Check if required commands exist
if ! command_exists ffmpeg; then
    print_error "FFmpeg is not installed. Please install FFmpeg to run this test."
    exit 1
fi
print_status "FFmpeg is available"

if ! command_exists nc; then
    print_warning "netcat (nc) is not available. Some tests may be skipped."
fi

# Check if Go is available and build the project
if ! command_exists go; then
    print_error "Go is not installed"
    exit 1
fi
print_status "Go is available"

echo "2. Building the streaming server..."
if ! go build -o streamer ./cmd/live-streaming; then
    print_error "Failed to build the streaming server"
    exit 1
fi
print_status "Server built successfully"

echo "3. Checking if ports are available..."
if ! port_available $RTMP_PORT; then
    print_error "Port $RTMP_PORT is already in use"
    exit 1
fi
print_status "RTMP port $RTMP_PORT is available"

if ! port_available $HTTP_PORT; then
    print_error "Port $HTTP_PORT is already in use"
    exit 1
fi
print_status "HTTP port $HTTP_PORT is available"

echo "4. Starting the streaming server..."
./streamer &
SERVER_PID=$!

# Wait for server to start
if ! wait_for_port $RTMP_PORT 10; then
    print_error "Server failed to start on port $RTMP_PORT"
    exit 1
fi
print_status "RTMP server is listening on port $RTMP_PORT"

if ! wait_for_port $HTTP_PORT 10; then
    print_error "HTTP server failed to start on port $HTTP_PORT"
    exit 1
fi
print_status "HTTP server is listening on port $HTTP_PORT"

echo "5. Testing RTMP connection..."
if command_exists nc; then
    if echo "" | nc -w 1 localhost $RTMP_PORT; then
        print_status "RTMP port is accepting connections"
    else
        print_error "RTMP port is not accepting connections"
        exit 1
    fi
fi

echo "6. Creating test stream with FFmpeg..."
# Create HLS directory if it doesn't exist
mkdir -p ./hls

# Start FFmpeg streaming
ffmpeg -f lavfi -i testsrc=duration=$TEST_DURATION:size=320x240:rate=30 \
       -f lavfi -i sine=frequency=1000:duration=$TEST_DURATION \
       -c:v libx264 -preset ultrafast -tune zerolatency \
       -c:a aac -b:a 128k \
       -f flv rtmp://localhost:$RTMP_PORT/live/$STREAM_KEY \
       -y > /dev/null 2>&1 &
FFMPEG_PID=$!

print_status "FFmpeg streaming started (PID: $FFMPEG_PID)"

echo "7. Waiting for HLS files to be generated..."
HLS_DIR="./hls/live/$STREAM_KEY"
PLAYLIST_FILE="$HLS_DIR/stream.m3u8"

# Wait for FFmpeg to complete first
echo "Waiting for FFmpeg to complete..."
wait $FFMPEG_PID 2>/dev/null || true
print_status "FFmpeg stream completed"

# Wait for HLS files to appear
WAIT_COUNT=0
MAX_WAIT=10
while [ $WAIT_COUNT -lt $MAX_WAIT ]; do
    if [ -f "$PLAYLIST_FILE" ]; then
        print_status "HLS playlist created: $PLAYLIST_FILE"
        break
    fi
    sleep 0.5
    ((WAIT_COUNT++))
done

if [ ! -f "$PLAYLIST_FILE" ]; then
    print_error "HLS playlist was not created within ${MAX_WAIT} seconds"
    exit 1
fi

echo "8. Checking HLS files..."
if [ -d "$HLS_DIR" ]; then
    FILE_COUNT=$(ls -1 "$HLS_DIR"/*.ts 2>/dev/null | wc -l)
    if [ $FILE_COUNT -gt 0 ]; then
        print_status "Found $FILE_COUNT HLS segment files"
    else
        print_warning "No HLS segment files found yet"
    fi
else
    print_error "HLS directory was not created"
    exit 1
fi

echo "9. Testing HTTP playlist access..."
if command_exists curl; then
    HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$HTTP_PORT/live/$STREAM_KEY.m3u8")
    if [ "$HTTP_RESPONSE" = "200" ]; then
        print_status "HTTP playlist is accessible (HTTP $HTTP_RESPONSE)"
    else
        print_warning "HTTP playlist returned status $HTTP_RESPONSE"
    fi
else
    print_warning "curl not available, skipping HTTP test"
fi

echo "10. Final verification..."
if [ -f "$PLAYLIST_FILE" ]; then
    PLAYLIST_SIZE=$(wc -l < "$PLAYLIST_FILE")
    if [ $PLAYLIST_SIZE -gt 5 ]; then
        print_status "Playlist contains $PLAYLIST_SIZE lines (looks good)"
    else
        print_warning "Playlist only contains $PLAYLIST_SIZE lines"
    fi
fi

# Check total file size
if [ -d "$HLS_DIR" ]; then
    TOTAL_SIZE=$(du -sh "$HLS_DIR" | cut -f1)
    print_status "Total HLS files size: $TOTAL_SIZE"
fi

echo ""
echo "🎉 Test Results Summary:"
echo "======================="
print_status "RTMP server started successfully"
print_status "FFmpeg streaming completed"
print_status "HLS files generated"
print_status "HTTP server responding"

echo ""
echo "📁 Generated files are in: $HLS_DIR"
echo "🌐 Playlist URL: http://localhost:$HTTP_PORT/live/$STREAM_KEY.m3u8"
echo ""
echo "✅ All tests passed! Your RTMP streaming server is working correctly."
