2025/07/03 08:04:54.178526 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:04:54.178560 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:04:54.178699 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:04:54.178798 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:04:54.178836 [22;36m INFO [0m     start: 2025-07-03 08:04:54.17 - base.go:35
2025/07/03 08:04:54.178862 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:04:54.178871 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:04:54.178879 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:04:54.178885 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:04:54.178891 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:04:54.178897 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:04:54.179054 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:04:54.179226 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:05:16.480206 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:33134 - server.go:95
2025/07/03 08:05:16.480310 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000114010, naza.Connection=0xc00014a000 - connection.go:193
2025/07/03 08:05:16.480351 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc000154000, remote addr=[::1]:33134 - server_session.go:113
2025/07/03 08:05:16.480409 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:05:16.480436 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:05:16.480452 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:05:16.480549 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:05:16.521652 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:05:16.521752 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:05:16.521792 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:05:16.521819 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:05:16.521834 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:05:16.563625 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.563687 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.563707 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:05:16.563723 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:05:16.604602 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:05:16.604710 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test_stream') - server_session.go:475
2025/07/03 08:05:16.604729 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:05:16.604936 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc000384008, appName=live, streamName=test_stream - group__.go:185
2025/07/03 08:05:16.604988 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:05:16.605132 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:05:16.605181 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:05:16.605221 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc000149590, streamName=test_stream - muxer.go:116
2025/07/03 08:05:16.605243 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:05:16.634092 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=13, value=duration: 0
width: 320
height: 240
videodatarate: 0
framerate: 30
videocodecid: 7
audiodatarate: 67.3828125
audiosamplerate: 44100
audiosamplesize: 16
stereo: false
audiocodecid: 10
encoder: Lavf58.76.100
filesize: 0
 - group__core_streaming.go:190
2025/07/03 08:05:16.634199 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:321 - gop_cache.go:93
2025/07/03 08:05:16.634245 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:61 - gop_cache.go:115
2025/07/03 08:05:16.634304 [22;34mDEBUG [0msps={ProfileIdc:244 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:13 SpsId:0 ChromaFormatIdc:3 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:2 Log2MaxPicOrderCntLsb:0 NumRefFrames:1 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:19 PicHeightInMapUnitsMinusOne:14 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:0 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:0 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:05:16.634363 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:05:16.653005 [22;33m WARN [0m[RTMP2MPEGTS1] rtmp msg too short, ignore. header={Csid:6 MsgLen:5 MsgTypeId:9 MsgStreamId:1 TimestampAbs:2990}, payload=00000000  17 02 00 00 00                                    |.....|
 - rtmp2mpegts.go:193
2025/07/03 08:05:16.653043 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCUnpublish, header={Csid:3 MsgLen:38 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=23, wpos=38, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.653056 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=deleteStream, header={Csid:3 MsgLen:34 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=24, wpos=34, hex=00000000  05 00 3f f0 00 00 00 00  00 00                    |..?.......|
 - server_session.go:366
2025/07/03 08:05:16.653072 [22;34mDEBUG [0m[NAZACONN1] close once. err=EOF - connection.go:509
2025/07/03 08:05:16.653119 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=EOF - server_session.go:549
2025/07/03 08:05:16.653129 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:05:16.653138 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:05:16.653150 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:05:17.179509 [22;36m INFO [0merase inactive group. [GROUP1] - server_manager__.go:316
2025/07/03 08:05:17.179568 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:05:17.244717 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:33142 - server.go:95
2025/07/03 08:05:17.244779 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc00022a000, naza.Connection=0xc0000ca2c0 - connection.go:193
2025/07/03 08:05:17.244817 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc0001baea0, remote addr=[::1]:33142 - server_session.go:113
2025/07/03 08:05:17.244865 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:05:17.244888 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:05:17.244900 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:05:17.245041 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:05:17.286637 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:05:17.286717 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:05:17.286775 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:05:17.286846 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:05:17.286879 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:05:17.328561 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:05:17.328623 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:05:17.328640 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:05:17.369657 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:17.369724 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test_stream'). - server_session.go:509
2025/07/03 08:05:17.369782 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:05:17.369879 [22;36m INFO [0m[GROUP2] lifecycle new group. group=0xc0000dc008, appName=live, streamName=test_stream - group__.go:185
2025/07/03 08:05:17.369904 [22;34mDEBUG [0m[GROUP2] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:05:34.179723 [22;33m WARN [0m[GROUP2] session timeout. session=RTMPPUBSUB2 - group__.go:468
2025/07/03 08:05:34.179860 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:05:34.179897 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:05:34.179950 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:05:34.180113 [22;34mDEBUG [0m[GROUP2] [RTMPPUBSUB2] del rtmp SubSession from group. - group__out_sub.go:137
2025/07/03 08:05:35.180111 [22;36m INFO [0merase inactive group. [GROUP2] - server_manager__.go:316
2025/07/03 08:05:35.180176 [22;36m INFO [0m[GROUP2] lifecycle dispose group. - group__.go:222
2025/07/03 08:05:40.654540 [22;36m INFO [0mcleanup hls file path. streamName=test_stream, path=hls/lal_hls/test_stream - server_manager__.go:758
2025/07/03 08:06:18.036739 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:06:18.036788 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:06:18.036948 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:06:18.037056 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:06:18.037086 [22;36m INFO [0m     start: 2025-07-03 08:06:18.035 - base.go:35
2025/07/03 08:06:18.037112 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:06:18.037122 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:06:18.037132 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:06:18.037141 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:06:18.037151 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:06:18.037160 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:06:18.037302 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:06:18.037346 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:06:21.894288 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:41570 - server.go:95
2025/07/03 08:06:21.894335 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc00019c238, naza.Connection=0xc0003a4160 - connection.go:193
2025/07/03 08:06:21.894356 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0001b3a00, remote addr=127.0.0.1:41570 - server_session.go:113
2025/07/03 08:06:21.894378 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:06:21.894395 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:06:21.894405 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:06:21.894476 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:06:21.935615 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:06:21.935692 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:06:21.935767 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:06:21.935818 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:06:21.935863 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:06:21.935991 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:21.976554 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:21.976590 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:06:21.976598 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:06:21.976683 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:06:21.976696 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:06:21.976704 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:06:21.976777 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc0001d2e08, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:06:21.976797 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:06:21.976901 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:06:21.976917 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:06:21.976938 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003e04b0, streamName=test-stream - muxer.go:116
2025/07/03 08:06:21.976949 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:06:21.977051 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:06:21.977077 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:06:22.633513 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:06:22.633654 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:06:22.633736 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:06:26.723246 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:27.149974 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:37076 - server.go:95
2025/07/03 08:06:27.150015 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc00019c000, naza.Connection=0xc0000ca210 - connection.go:193
2025/07/03 08:06:27.150033 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc0000da000, remote addr=[::1]:37076 - server_session.go:113
2025/07/03 08:06:27.150056 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:06:27.150070 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:06:27.150077 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:06:27.150203 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:06:27.190581 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:06:27.190619 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:06:27.190645 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:06:27.190659 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:06:27.190670 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:06:27.231564 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:06:27.231642 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:06:27.231705 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:06:27.272630 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:27.272689 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test-stream'). - server_session.go:509
2025/07/03 08:06:27.272727 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:06:27.272789 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:06:27.273287 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write metadata - group__core_streaming.go:253
2025/07/03 08:06:27.273308 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write vsh - group__core_streaming.go:257
2025/07/03 08:06:27.273319 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write ash - group__core_streaming.go:261
2025/07/03 08:06:29.773239 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:32.273918 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:34.723276 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:37.923272 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:38.851286 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=2511164. - server_session.go:272
2025/07/03 08:06:43.023081 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:46.872795 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=5019216. - server_session.go:272
2025/07/03 08:06:54.873242 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=7531258. - server_session.go:272
2025/07/03 08:07:02.853326 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=10041664. - server_session.go:272
2025/07/03 08:07:05.572971 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:07:08.323620 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:07:10.854772 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=12600460. - server_session.go:272
2025/07/03 08:07:15.491538 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCUnpublish, header={Csid:3 MsgLen:38 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=23, wpos=38, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:07:15.491601 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=deleteStream, header={Csid:3 MsgLen:34 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=24, wpos=34, hex=00000000  05 00 3f f0 00 00 00 00  00 00                    |..?.......|
 - server_session.go:366
2025/07/03 08:07:15.491622 [22;34mDEBUG [0m[NAZACONN1] close once. err=EOF - connection.go:509
2025/07/03 08:07:15.491689 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=EOF - server_session.go:549
2025/07/03 08:07:15.491705 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:07:15.491721 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:07:15.491754 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:07:28.037570 [22;33m WARN [0m[GROUP1] session timeout. session=RTMPPUBSUB2 - group__.go:468
2025/07/03 08:07:28.037658 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:07:28.037678 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:07:28.037698 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:07:28.037807 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] del rtmp SubSession from group. - group__out_sub.go:137
2025/07/03 08:07:29.038113 [22;36m INFO [0merase inactive group. [GROUP1] - server_manager__.go:316
2025/07/03 08:07:29.038171 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:07:39.493983 [22;36m INFO [0mcleanup hls file path. streamName=test-stream, path=hls/lal_hls/test-stream - server_manager__.go:758
2025/07/03 08:08:15.965537 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:15.965640 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:15.965702 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:18.760713 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:18.760751 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:18.760946 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:18.761079 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:18.761126 [22;36m INFO [0m     start: 2025-07-03 08:08:18.758 - base.go:35
2025/07/03 08:08:18.761156 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:18.761169 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:18.761182 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:18.761195 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:18.761207 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:18.761219 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:18.761367 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:18.761412 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:22.112083 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:22.112184 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:22.112240 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:37.555707 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:37.555757 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:37.555925 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:37.556067 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:37.556093 [22;36m INFO [0m     start: 2025-07-03 08:08:37.554 - base.go:35
2025/07/03 08:08:37.556121 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:37.556131 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:37.556142 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:37.556152 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:37.556162 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:37.556177 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:37.556355 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:37.556397 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:39.663672 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:39.663804 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:39.663859 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:50.946965 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:50.947016 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:50.947277 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:50.947383 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:50.947419 [22;36m INFO [0m     start: 2025-07-03 08:08:50.945 - base.go:35
2025/07/03 08:08:50.947445 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:50.947455 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:50.947473 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:50.947487 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:50.947497 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:50.947506 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:50.947651 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:50.947696 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:56.157939 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:51916 - server.go:95
2025/07/03 08:08:56.157993 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc00029e018, naza.Connection=0xc0002c8000 - connection.go:193
2025/07/03 08:08:56.158019 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0002ce000, remote addr=127.0.0.1:51916 - server_session.go:113
2025/07/03 08:08:56.158038 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:08:56.158047 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:08:56.158054 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:08:56.158123 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:08:56.198572 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:08:56.198649 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:08:56.198701 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:08:56.198734 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:08:56.198765 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:08:56.198933 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:08:56.239555 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:08:56.239597 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:08:56.239605 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:08:56.239676 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:08:56.239687 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:08:56.239695 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:08:56.239781 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc000504008, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:08:56.239801 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:08:56.239845 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:08:56.239859 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:08:56.239877 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0002c7590, streamName=test-stream - muxer.go:116
2025/07/03 08:08:56.239888 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:08:56.240025 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:08:56.240053 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:08:56.891071 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:08:56.891155 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:08:56.891191 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:09:00.989302 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:01.734603 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:02.019240 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:35564 - server.go:95
2025/07/03 08:09:02.019280 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000204048, naza.Connection=0xc0002c8160 - connection.go:193
2025/07/03 08:09:02.019301 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00013fa00, remote addr=[::1]:35564 - server_session.go:113
2025/07/03 08:09:02.019324 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:09:02.019339 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:09:02.019348 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:09:02.019508 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:09:02.059593 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:09:02.059645 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:09:02.059680 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:09:02.059696 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:09:02.059710 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:09:02.100530 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:09:02.100581 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:09:02.100601 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:09:02.142570 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:09:02.142615 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test-stream'). - server_session.go:509
2025/07/03 08:09:02.142655 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:09:02.142701 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:09:02.156880 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write metadata - group__core_streaming.go:253
2025/07/03 08:09:02.156927 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write vsh - group__core_streaming.go:257
2025/07/03 08:09:02.156936 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write ash - group__core_streaming.go:261
2025/07/03 08:09:04.640239 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:07.240575 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:07.690837 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:09.585619 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:12.649683 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=2501472. - server_session.go:272
2025/07/03 08:09:12.790434 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:13.790260 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:15.989195 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:17.286833 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:20.190674 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:20.265752 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=5039141. - server_session.go:272
2025/07/03 08:09:21.640229 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:22.090337 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:23.689807 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:26.440384 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:28.672805 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=7539318. - server_session.go:272
2025/07/03 08:09:28.789755 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:29.640738 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:30.539872 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:36.606813 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=10041752. - server_session.go:272
2025/07/03 08:09:44.190389 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:44.556581 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=12542883. - server_session.go:272
2025/07/03 08:09:45.789788 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:46.390248 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:49.290778 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:52.540394 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=15046277. - server_session.go:272
2025/07/03 08:09:55.540002 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:59.039494 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:00.457122 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=17552582. - server_session.go:272
2025/07/03 08:10:02.240293 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:05.585232 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:08.173853 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=20086146. - server_session.go:272
2025/07/03 08:10:10.239856 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:11.989711 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.190662 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.340251 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.499545 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=22726201. - server_session.go:272
2025/07/03 08:10:23.190773 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:24.790660 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:24.840567 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=25328651. - server_session.go:272
2025/07/03 08:10:27.049334 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:10:27.049404 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:10:27.049437 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:10:27.049447 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:10:27.049470 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:10:27.049510 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:10:27.049523 [22;34mDEBUG [0m[NAZACONN1] close once. err=<nil> - connection.go:509
2025/07/03 08:10:27.049569 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:10:27.049582 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:10:27.049588 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:10:27.049632 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:10:27.050009 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:13:12.507568 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:13:12.507597 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:13:12.507714 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:13:12.507798 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:13:12.507822 [22;36m INFO [0m     start: 2025-07-03 08:13:12.506 - base.go:35
2025/07/03 08:13:12.507841 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:13:12.507847 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:13:12.507854 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:13:12.507860 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:13:12.507867 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:13:12.507872 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:13:12.507968 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:13:12.507996 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:13:15.590894 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:59712 - server.go:95
2025/07/03 08:13:15.590985 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000128238, naza.Connection=0xc0003aa160 - connection.go:193
2025/07/03 08:13:15.591021 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc00013fa00, remote addr=[::1]:59712 - server_session.go:113
2025/07/03 08:13:15.591093 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:13:15.591137 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:13:15.591152 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:13:15.591311 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:13:15.631606 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:13:15.631684 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:13:15.631744 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:13:15.631780 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:13:15.631841 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:13:15.672549 [22;36m INFO [0m[RTMPPUBSUB1] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:13:15.672599 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:13:15.672610 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:13:15.713617 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:13:15.713682 [22;36m INFO [0m[RTMPPUBSUB1] < R play('test-stream'). - server_session.go:509
2025/07/03 08:13:15.713750 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:13:15.713881 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc00015ae08, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:13:15.713915 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:13:20.396527 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:33604 - server.go:95
2025/07/03 08:13:20.396589 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000128248, naza.Connection=0xc0003aa370 - connection.go:193
2025/07/03 08:13:20.396621 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00013fba0, remote addr=127.0.0.1:33604 - server_session.go:113
2025/07/03 08:13:20.396650 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:13:20.396724 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:13:20.396749 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:13:20.396930 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:13:20.438587 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:13:20.438656 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:13:20.438714 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:13:20.438760 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:13:20.438789 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:13:20.438943 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:13:20.479585 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:13:20.479655 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:13:20.479671 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:13:20.479786 [22;34mDEBUG [0m[RTMPPUBSUB2] pubType=live - server_session.go:474
2025/07/03 08:13:20.479806 [22;36m INFO [0m[RTMPPUBSUB2] < R publish('test-stream') - server_session.go:475
2025/07/03 08:13:20.479820 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:13:20.479921 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:13:20.480018 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:13:20.480051 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:13:20.480074 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003ed680, streamName=test-stream - muxer.go:116
2025/07/03 08:13:20.480114 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:13:20.480179 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:13:20.480235 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:13:21.124261 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:13:21.124337 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:13:21.124387 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:13:29.173658 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:29.181953 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=2502419. - server_session.go:272
2025/07/03 08:13:34.674829 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.274511 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.574208 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.723282 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.874273 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:37.023573 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:37.139927 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=5003055. - server_session.go:272
2025/07/03 08:13:37.173602 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:38.624456 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:38.774392 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.473591 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.623888 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.773962 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.923987 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:40.073561 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:40.224039 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:40.374035 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.073199 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.223443 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.373511 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.523559 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.672432 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:42.674388 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:42.824202 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:42.974549 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.124182 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.273168 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.423555 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.573378 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.274128 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.423254 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.573566 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.723656 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.874053 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:45.023541 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:45.090494 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=7503915. - server_session.go:272
2025/07/03 08:13:45.173652 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:45.873713 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:46.624120 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:46.773740 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.472968 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.623194 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.773229 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.923191 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:48.073110 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:48.223357 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:48.373330 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.073652 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.223329 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.373315 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.523279 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.674158 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.823393 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.973164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:50.823715 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:50.973689 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.122898 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.271978 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.421065 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.570054 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.273529 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.423311 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.573693 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.723610 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.873676 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:53.023290 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:53.038526 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=10004752. - server_session.go:272
2025/07/03 08:13:53.174078 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:53.873059 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:54.023441 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:54.173951 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.472907 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.623155 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.774125 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.924114 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:56.073643 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:56.223197 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:56.373362 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.073751 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.223334 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.373772 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.523607 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.673828 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.823553 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.973931 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.123725 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.273679 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.423579 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.573506 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.274824 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.422985 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.573443 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.723136 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.872152 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.956719 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=12550224. - server_session.go:272
2025/07/03 08:14:02.173235 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:03.623536 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:03.773510 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:03.923572 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:04.073445 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:04.223346 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:04.373764 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.073586 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.223355 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.373569 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.523494 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.673554 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.823279 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.973594 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.122947 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.273089 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.423143 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.573217 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.272815 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.423026 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.572905 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.722967 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.873172 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:09.024077 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:09.088545 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=15053585. - server_session.go:272
2025/07/03 08:14:09.173577 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:09.873590 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.023256 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.173653 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.322884 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.472116 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.620990 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.473494 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.623164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.773619 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.923354 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:12.073531 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:12.223649 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:12.373164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.073001 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.223162 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.373145 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.523025 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.673123 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.823282 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.973219 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:14.673039 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:14.823521 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:15.573621 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.273678 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.423614 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.573392 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.723314 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.873567 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:17.024457 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:17.026224 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=17554451. - server_session.go:272
2025/07/03 08:14:17.173691 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:17.873816 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.023308 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.173461 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.323213 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.473334 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.622107 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.771256 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:19.622521 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:19.772912 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:19.922927 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:20.222785 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:20.373055 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.073533 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.223891 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.373559 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.523216 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.673540 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.823677 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.973786 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:22.673564 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:22.823435 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:22.973461 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:23.123892 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.273164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.423051 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.573406 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.723186 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.873408 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.984150 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=20055309. - server_session.go:272
2025/07/03 08:14:25.023959 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:25.173942 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:25.873530 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.023636 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.173529 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.323682 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.473387 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.622929 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.771921 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:28.073542 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:28.223376 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:28.373667 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.073355 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.223361 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.373146 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.523236 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.673399 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.823206 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.973561 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:30.823197 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:30.973567 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:31.124234 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:31.273319 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:31.423148 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:32.906268 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=22561712. - server_session.go:272
2025/07/03 08:14:34.325235 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:34.474295 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:34.623639 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:35.474694 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:35.623569 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:36.073228 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:36.223258 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.090495 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.239507 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.389344 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.538515 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.687119 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.835611 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.984503 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:38.687342 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:38.837090 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:38.986002 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.134995 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.283948 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.433254 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.581852 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.475840 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.589655 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.739907 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.890258 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.922529 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=25062182. - server_session.go:272
2025/07/03 08:14:41.040391 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:41.189968 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:41.889777 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.040298 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.189571 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.340069 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.489794 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.640029 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.789738 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.490031 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.639845 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.790239 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.940252 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.091997 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.241050 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.390280 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.540115 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.690432 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.839896 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.990348 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:46.691028 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:46.840072 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:46.990030 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.139764 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.289548 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.440049 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.590143 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:48.873395 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=27563060. - server_session.go:272
2025/07/03 08:14:48.889960 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:49.890407 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.040159 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.190523 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.340094 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.490331 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.640025 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.790087 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.490332 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.639983 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.790188 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.940086 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:52.090604 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:52.240453 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:53.990008 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:56.440192 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:56.573715 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=30064399. - server_session.go:272
2025/07/03 08:14:56.890362 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:57.890259 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.040050 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.190562 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.340410 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.490320 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.640181 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.790349 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.490222 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.640175 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.789950 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.940045 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:00.090763 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:00.240664 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:00.389748 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.240104 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.390582 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.540221 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.689792 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.840030 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.989907 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:02.692737 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:02.841618 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:02.990662 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.140309 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.290092 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.440097 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.590369 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.290352 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.439808 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.589661 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.740105 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.773502 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=32564658. - server_session.go:272
2025/07/03 08:15:05.723034 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:05.890123 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.040366 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.189841 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.339917 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.489840 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.639836 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.789788 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:07.490232 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:07.639777 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:07.791238 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:09.839712 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:09.990566 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:10.991676 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.140595 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.291261 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.440149 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.591413 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:12.441056 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:12.723302 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=35065433. - server_session.go:272
2025/07/03 08:15:13.040034 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:13.190241 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:13.890196 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.040055 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.190539 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.340452 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.491050 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.639942 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.790457 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.489935 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.640131 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.790338 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.940356 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:16.090271 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:16.271824 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.106672 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.256709 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.406935 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.557077 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.706374 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.856926 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:18.007195 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:18.714617 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:18.863268 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.012079 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.161105 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.320405 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.458720 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.607588 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:31.278056 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:16:31.278129 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:16:31.278318 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:16:31.278428 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 500, "fragment_num": 3, "delete_threshold": 3, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:500 FragmentNum:3 DeleteThreshold:3 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:16:31.278451 [22;36m INFO [0m     start: 2025-07-03 08:16:31.275 - base.go:35
2025/07/03 08:16:31.278479 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:16:31.278491 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:16:31.278502 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:16:31.278515 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:16:31.278524 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:16:31.278533 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:16:31.278668 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:16:31.278699 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:16:34.367188 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:37168 - server.go:95
2025/07/03 08:16:34.367256 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000216260, naza.Connection=0xc0003a0160 - connection.go:193
2025/07/03 08:16:34.367274 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc00022da00, remote addr=[::1]:37168 - server_session.go:113
2025/07/03 08:16:34.367299 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:16:34.367313 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:16:34.367320 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:16:34.367434 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:16:34.407567 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:16:34.407646 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:16:34.407689 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:16:34.407709 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:16:34.407726 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:16:34.448568 [22;36m INFO [0m[RTMPPUBSUB1] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:16:34.448632 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:16:34.448650 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:16:34.489606 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:16:34.489671 [22;36m INFO [0m[RTMPPUBSUB1] < R play('test-stream'). - server_session.go:509
2025/07/03 08:16:34.489711 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:16:34.489778 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc00024ee08, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:16:34.489793 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:16:37.262179 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:57524 - server.go:95
2025/07/03 08:16:37.262239 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000412000, naza.Connection=0xc000422000 - connection.go:193
2025/07/03 08:16:37.262273 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00042c000, remote addr=127.0.0.1:57524 - server_session.go:113
2025/07/03 08:16:37.262297 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:16:37.262313 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:16:37.262323 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:16:37.262404 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:16:37.302579 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:16:37.302641 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:16:37.302673 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:16:37.302689 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:16:37.302710 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:16:37.302857 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:16:37.343585 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:16:37.343666 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:16:37.343696 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:16:37.343882 [22;34mDEBUG [0m[RTMPPUBSUB2] pubType=live - server_session.go:474
2025/07/03 08:16:37.343904 [22;36m INFO [0m[RTMPPUBSUB2] < R publish('test-stream') - server_session.go:475
2025/07/03 08:16:37.343924 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:16:37.344038 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:16:37.344084 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:16:37.344104 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:16:37.344130 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003e30e0, streamName=test-stream - muxer.go:116
2025/07/03 08:16:37.344148 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:16:37.344330 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:16:37.344404 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:16:37.990804 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:16:37.990894 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:16:37.990933 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:16:40.790264 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:42.090211 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:46.040566 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=2502152. - server_session.go:272
2025/07/03 08:16:48.339822 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:50.390402 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:52.435038 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:53.988616 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=5002959. - server_session.go:272
2025/07/03 08:17:01.956688 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=7503598. - server_session.go:272
2025/07/03 08:17:09.890104 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=10004137. - server_session.go:272
2025/07/03 08:17:13.239841 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:15.840180 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:17.841924 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=12504692. - server_session.go:272
2025/07/03 08:17:17.884498 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:21.239927 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.390069 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.540228 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.690365 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.839810 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.540438 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.690679 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.840411 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.990581 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:24.140640 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:24.290572 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:24.440150 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.140020 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.289454 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.440376 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.589504 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.633627 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=15036182. - server_session.go:272
2025/07/03 08:17:26.739875 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:26.889713 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.038691 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.187794 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.336833 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.634788 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.336983 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.489798 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.640050 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.790258 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.940056 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:29.089848 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:29.240103 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:30.389981 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:30.540846 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:30.840866 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.541947 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.690438 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.840022 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.989752 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:32.139720 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:32.289997 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:32.440089 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.139929 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.289884 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.439674 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.590094 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.740186 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.849097 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=17537756. - server_session.go:272
2025/07/03 08:17:33.890710 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:34.890199 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.039799 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.189090 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.338585 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.487416 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.636293 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.339271 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.488267 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.637661 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.786935 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.936021 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:37.085127 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:37.233984 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:37.939922 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:38.090195 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:38.840258 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.539757 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.690215 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.840058 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.990302 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:40.141076 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:40.290096 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:40.440182 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.140188 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.289971 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.440371 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.589744 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.739969 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.806852 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=20038613. - server_session.go:272
2025/07/03 08:17:41.889766 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:42.039739 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:42.890160 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.040519 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.190459 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.340428 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.490111 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.639953 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.340231 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.490308 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.640346 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.790021 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.940644 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:45.089609 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:45.238623 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:45.939982 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:46.090697 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:46.239960 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:46.388915 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:47.537460 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:49.740353 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:49.751511 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=22539359. - server_session.go:272
2025/07/03 08:17:50.740050 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:50.890549 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.040184 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.190275 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.340193 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.490011 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.640227 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.489676 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.639701 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.789816 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.939755 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:53.089949 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:55.989766 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:56.140070 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:56.290390 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.139944 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.289732 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.440185 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.589986 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.706601 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=25040109. - server_session.go:272
2025/07/03 08:17:57.755230 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.904591 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:58.053583 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:58.755075 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:59.202604 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:59.650274 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.353013 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.501880 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.651156 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.800609 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.949496 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:01.098413 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:01.247387 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:01.949970 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.098977 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.248083 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.396946 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.546062 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.695063 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.844111 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:03.695694 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:03.844866 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:03.994191 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:04.143363 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:04.292251 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:04.441416 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.144801 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.293985 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.443036 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.592537 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.673511 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=27541336. - server_session.go:272
2025/07/03 08:18:05.741650 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.890660 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:06.040525 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:06.743046 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:06.890916 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:07.040092 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.339886 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.490023 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.639795 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.789737 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.939730 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:09.089735 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:09.240048 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:09.956209 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.106777 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.255454 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.404710 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.553501 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.702328 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.851229 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.000995 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.149809 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.298984 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.447741 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.150655 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.299817 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.448580 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.597660 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.608587 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=30041834. - server_session.go:272
2025/07/03 08:18:13.746727 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.896050 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:14.052854 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:14.754983 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:14.904388 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:15.053315 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:15.202398 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:15.351114 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.351280 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.500208 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.649236 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.798060 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.947149 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:19.544356 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:19.991675 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:20.140433 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:20.290802 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:20.440345 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:21.143150 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:21.557053 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=32542599. - server_session.go:272
2025/07/03 08:18:21.589761 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:21.890481 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:22.039723 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:22.739546 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:22.890015 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.040409 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.189493 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.355249 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.504572 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.653506 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.355689 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.504725 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.653525 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.802864 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.951925 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:25.101356 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:25.250700 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:25.953153 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.102261 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.251401 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.400281 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.549131 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.698034 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.846885 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.550175 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.699137 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.847946 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.997032 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:28.146150 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:28.295157 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:28.444168 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.147271 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.296464 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.445604 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.509793 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=35043502. - server_session.go:272
2025/07/03 08:18:30.745204 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:30.894183 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.043321 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.192287 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.341478 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.490749 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.640710 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.342015 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.491133 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.640528 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.790027 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.940596 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:33.090322 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:33.240378 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.390083 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.539938 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.689683 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.839967 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.540520 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.690139 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.840739 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.990811 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:36.155179 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:36.304217 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:36.453030 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:37.155202 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:37.304484 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:37.453426 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:37.457158 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=37544340. - server_session.go:272
2025/07/03 08:18:37.603023 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:37.752103 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:38.753276 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:38.902601 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:39.051907 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:39.201047 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:39.349868 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:39.499140 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:39.647996 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:40.350950 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:40.500315 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:40.649108 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:40.798229 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:40.947255 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:41.096448 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:41.245562 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:41.948018 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:42.841795 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:43.545158 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:43.694265 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:43.843148 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:43.992229 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:44.141248 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:44.290542 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:44.440058 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:45.142818 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:45.291954 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:45.407736 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=40045216. - server_session.go:272
2025/07/03 08:18:45.441143 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:45.590823 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:45.740320 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:45.890363 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:46.040375 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:46.890766 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:47.040945 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:47.190881 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:47.340392 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:47.490720 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:47.640580 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:48.357098 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:48.506174 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:48.654911 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:48.803709 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:48.952688 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:49.101792 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:49.251020 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:49.953414 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:50.102389 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:50.251674 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:51.550061 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:51.698944 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:51.847996 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:51.997139 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:52.146209 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:52.295038 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:52.443915 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:53.146674 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:53.295758 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:53.359685 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=42546071. - server_session.go:272
2025/07/03 08:18:53.444671 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:53.593633 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:53.742539 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:53.891523 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:54.040544 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:55.340768 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:55.490776 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:55.640589 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:56.340426 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:56.490259 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:56.640973 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:56.790761 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:56.940544 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:57.090644 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:57.240433 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:57.940990 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:58.107089 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:58.256249 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:58.405241 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:58.554088 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:58.702931 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:59.555057 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:59.704394 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:59.853436 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:00.002529 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:00.151374 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:00.300755 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:00.449756 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:01.152124 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:01.301238 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:01.307861 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=45046927. - server_session.go:272
2025/07/03 08:19:01.450323 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:01.599314 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:01.748397 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:01.897322 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:02.046360 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:02.748405 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:03.493352 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:04.344958 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:04.493973 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:04.643024 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:04.792057 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:04.941278 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:05.090614 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:05.240411 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:05.942214 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:06.091272 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:06.240747 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:06.390849 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:06.540807 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:06.690621 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:06.840947 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:07.691077 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:07.840406 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:07.990341 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:08.140615 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:08.290348 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:08.454039 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:09.155562 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:09.261840 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=47547786. - server_session.go:272
2025/07/03 08:19:09.304480 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:09.453545 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:09.602843 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:09.751747 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:09.900835 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:10.050116 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:10.752673 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:10.901403 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:11.050182 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:11.199010 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:12.347666 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:12.496639 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:12.645507 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:12.794414 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:12.943333 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:13.092294 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:13.241414 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:13.944463 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:14.093585 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:14.242767 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:14.391707 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:14.540944 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:14.690737 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:14.840799 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:15.990085 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:16.140787 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:16.290566 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:16.440547 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:17.140640 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:17.207520 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=50048663. - server_session.go:272
2025/07/03 08:19:17.290614 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:17.440648 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:17.590502 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:17.740430 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:17.890653 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:18.054019 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:18.755970 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:18.905062 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:19.054173 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:19.202996 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:19.352205 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:19.501198 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:20.352450 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:20.501579 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:20.799759 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:20.948517 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:21.097327 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:21.246186 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:21.948698 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:22.098095 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:22.247048 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:22.395805 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:22.545010 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:22.693957 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:22.842945 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:23.545295 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:23.694306 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:24.440332 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:25.142519 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:25.157395 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=52549500. - server_session.go:272
2025/07/03 08:19:25.291707 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:25.440747 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:25.590384 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:25.740701 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:25.890594 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:26.040876 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:26.740721 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:26.891324 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:27.041016 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:27.190865 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:27.340795 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:27.490520 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:27.640848 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:28.506044 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:28.803806 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:29.101971 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:29.251712 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:29.954676 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:30.103793 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:30.252957 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:30.402106 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:30.551203 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:30.700169 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:30.849100 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:31.551549 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:31.700028 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:31.849037 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:31.997982 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:33.107221 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=55050372. - server_session.go:272
2025/07/03 08:19:33.296321 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:33.445261 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:33.594145 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:33.743255 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:33.892249 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:34.041333 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:34.743519 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:34.892846 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:35.041978 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:35.190869 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:35.340891 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:35.490781 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:35.640476 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:36.940728 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:37.090624 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:37.940841 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:38.090258 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:38.240822 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:38.392306 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:38.540723 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:38.704718 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:38.853606 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:39.555797 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:39.705220 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:39.854414 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:40.003772 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:40.152790 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:40.301820 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:41.057333 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=57551232. - server_session.go:272
2025/07/03 08:19:41.140582 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:41.290753 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:41.441339 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:41.605895 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:41.755088 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:41.904344 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:42.052796 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:42.755547 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:42.904651 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:43.053875 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:43.203245 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:43.352312 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:43.501323 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:43.650086 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:44.353158 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:44.502005 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:45.247350 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:45.949457 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:46.098313 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:46.247215 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:46.396253 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:46.545196 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:46.694047 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:46.843065 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:47.546235 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:47.695863 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:47.844702 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:47.993668 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:48.142645 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:48.857101 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=60058887. - server_session.go:272
2025/07/03 08:19:56.443798 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:19:56.529049 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=62559903. - server_session.go:272
2025/07/03 08:20:00.640736 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:20:04.840919 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=65138506. - server_session.go:272
2025/07/03 08:20:08.941315 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:20:13.140043 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:20:13.140166 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=67642885. - server_session.go:272
2025/07/03 08:20:15.353108 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:20:21.091732 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=70148115. - server_session.go:272
2025/07/03 08:20:21.443002 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:21:45.485008 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:21:45.485041 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:21:45.485181 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:21:45.485260 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 250, "fragment_num": 2, "delete_threshold": 2, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:250 FragmentNum:2 DeleteThreshold:2 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:21:45.485285 [22;36m INFO [0m     start: 2025-07-03 08:21:45.483 - base.go:35
2025/07/03 08:21:45.485304 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:21:45.485311 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:21:45.485318 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:21:45.485324 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:21:45.485330 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:21:45.485336 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:21:45.485453 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:21:45.485498 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:21:49.725227 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:58900 - server.go:95
2025/07/03 08:21:49.725344 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000386010, naza.Connection=0xc0003b0000 - connection.go:193
2025/07/03 08:21:49.725405 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0003b8000, remote addr=127.0.0.1:58900 - server_session.go:113
2025/07/03 08:21:49.725448 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:21:49.725489 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:21:49.725549 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:21:49.725721 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:21:49.766698 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:21:49.766811 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:21:49.766855 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:21:49.766879 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:21:49.766897 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:21:49.766997 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:21:49.807580 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:21:49.807641 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:21:49.807659 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:21:49.807786 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:21:49.807807 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:21:49.807821 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:21:49.807948 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc0003ec008, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:21:49.807973 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:21:49.808087 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:21:49.808105 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:21:49.808125 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003af590, streamName=test-stream - muxer.go:116
2025/07/03 08:21:49.808141 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:21:49.808202 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:21:49.808253 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:21:50.457562 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:21:50.457669 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:21:50.457754 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:21:52.994177 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=0, ts=226440, frame=sid=224, dts=226440, pts=233910, cc=177, key=false, len=12820, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:21:54.557211 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:21:57.156382 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=374940, ts=601470, frame=sid=224, dts=601470, pts=601470, cc=204, key=false, len=3295, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:01.333774 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=749970, ts=976410, frame=sid=224, dts=976410, pts=983880, cc=83, key=false, len=12596, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:04.606372 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:04.756205 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:04.906582 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:05.490495 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=1124910, ts=1351440, frame=sid=224, dts=1351440, pts=1351440, cc=78, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:05.609716 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:05.757610 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:05.907055 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:06.056577 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:06.205511 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:06.354254 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:06.503048 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:07.357193 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:07.506871 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:07.657191 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:07.806967 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:07.956595 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:08.106796 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:08.807770 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:08.957722 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:09.107543 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:09.257460 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:09.407771 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:09.557705 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:09.657673 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=1499940, ts=1726470, frame=sid=224, dts=1726470, pts=1726470, cc=159, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:09.707630 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:10.408098 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:10.557751 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:10.707276 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:10.857904 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:11.007504 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:11.157787 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:12.007119 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:12.157409 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:12.307460 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:12.457235 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:12.606827 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:12.755660 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:12.904779 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:13.606770 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:13.755667 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:13.824400 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=1874970, ts=2101410, frame=sid=224, dts=2101410, pts=2101410, cc=240, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:13.907611 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:14.057388 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:14.207514 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:14.357301 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:14.506969 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:15.207589 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:15.357653 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:15.807520 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:16.107602 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:16.807284 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:16.957655 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:17.107675 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:17.257580 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:17.407694 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:17.557318 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:17.707390 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:17.991262 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=2249910, ts=2476440, frame=sid=224, dts=2476440, pts=2476440, cc=66, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:18.407479 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:18.557011 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:18.707538 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:18.857288 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:19.007221 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:19.157140 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:19.307122 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:20.007563 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:20.307687 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:20.457620 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:20.606270 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:20.755206 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:20.904319 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:21.606305 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:21.757211 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:21.914671 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:22.057218 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:22.160094 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=2624940, ts=2851470, frame=sid=224, dts=2851470, pts=2851470, cc=148, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:22.206680 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:22.356995 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:22.506698 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:23.207647 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:23.357417 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:23.507333 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:23.657415 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:24.107400 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:24.807160 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:24.957080 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:25.107561 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:25.257363 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:25.407504 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:25.557534 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:25.707325 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:28.606110 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:28.669378 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=3209940, ts=3436470, frame=sid=224, dts=3436470, pts=3443940, cc=179, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:28.757212 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:28.907176 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:29.623687 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:29.773792 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:29.924426 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:30.073288 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:30.222214 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:30.519728 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:31.221560 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:31.370340 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:31.519177 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:31.668085 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:31.816977 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:31.965802 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:32.114751 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:32.817056 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:32.838710 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=3584970, ts=3811410, frame=sid=224, dts=3811410, pts=3814380, cc=3, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:32.966009 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:33.114921 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:33.264068 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:33.413101 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:33.562200 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:33.711177 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:34.413766 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:34.712051 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:34.861103 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:35.010019 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:35.158736 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:35.307884 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:36.010109 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:36.307936 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:36.457462 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:36.607285 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:36.757193 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:36.907278 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:36.990048 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=3959910, ts=4186440, frame=sid=224, dts=4186440, pts=4189410, cc=82, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:37.623440 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:37.773753 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:37.923167 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:38.073288 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:38.222643 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:38.371434 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:38.520609 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:39.222596 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:39.371405 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:39.520363 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:39.668800 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:39.817710 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:39.966737 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:40.115611 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:40.818148 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:40.967052 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:41.116066 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:41.173521 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=4334940, ts=4561470, frame=sid=224, dts=4561470, pts=4561470, cc=164, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:41.265182 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:41.414185 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:41.563357 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:41.712196 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:42.414789 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:42.563765 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:42.712594 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:43.010691 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:43.159672 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:43.308793 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:44.011127 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:44.159868 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:44.308712 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:44.756812 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:45.329524 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=4709970, ts=4936410, frame=sid=224, dts=4936410, pts=4936410, cc=241, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:22:45.623693 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:45.773477 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:45.923599 [22;34mDEBUG [0m[0xc00039c270] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:46.045356 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:22:46.045480 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:22:46.045527 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:22:46.045527 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:22:46.045573 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:22:46.045585 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:22:46.045593 [22;34mDEBUG [0m[NAZACONN1] close once. err=<nil> - connection.go:509
2025/07/03 08:22:46.045687 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:22:55.730828 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:22:55.730859 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:22:55.731007 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:22:55.731125 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 250, "fragment_num": 2, "delete_threshold": 2, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:250 FragmentNum:2 DeleteThreshold:2 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:22:55.731150 [22;36m INFO [0m     start: 2025-07-03 08:22:55.729 - base.go:35
2025/07/03 08:22:55.731174 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:22:55.731184 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:22:55.731194 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:22:55.731203 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:22:55.731212 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:22:55.731221 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:22:55.731347 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:22:55.731384 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:22:56.068054 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:50604 - server.go:95
2025/07/03 08:22:56.068187 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000116030, naza.Connection=0xc000162000 - connection.go:193
2025/07/03 08:22:56.068243 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc000166000, remote addr=127.0.0.1:50604 - server_session.go:113
2025/07/03 08:22:56.068328 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:22:56.068348 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:22:56.068365 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:22:56.068483 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:22:56.109594 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:22:56.109673 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:22:56.109734 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:22:56.109764 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:22:56.109790 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:22:56.110053 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:22:56.150605 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:22:56.150701 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:22:56.150726 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:22:56.150882 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:22:56.150912 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:22:56.150934 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:22:56.151161 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc0003c2008, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:22:56.151243 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:22:56.151420 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:22:56.151448 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:22:56.151497 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003a0a50, streamName=test-stream - muxer.go:116
2025/07/03 08:22:56.151531 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:22:56.151656 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:22:56.151720 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:22:56.790772 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:22:56.790831 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:22:56.790865 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:22:57.525106 [22;34mDEBUG [0m[0xc00039e060] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:57.674278 [22;34mDEBUG [0m[0xc00039e060] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:22:59.890756 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCUnpublish, header={Csid:3 MsgLen:38 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=23, wpos=38, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:22:59.890839 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=deleteStream, header={Csid:3 MsgLen:34 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=24, wpos=34, hex=00000000  05 00 3f f0 00 00 00 00  00 00                    |..?.......|
 - server_session.go:366
2025/07/03 08:22:59.890884 [22;34mDEBUG [0m[NAZACONN1] close once. err=EOF - connection.go:509
2025/07/03 08:22:59.890980 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=EOF - server_session.go:549
2025/07/03 08:22:59.891012 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:22:59.891035 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:22:59.891091 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:23:00.359153 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:50616 - server.go:95
2025/07/03 08:23:00.359214 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000228008, naza.Connection=0xc0000ca2c0 - connection.go:193
2025/07/03 08:23:00.359250 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00023fba0, remote addr=127.0.0.1:50616 - server_session.go:113
2025/07/03 08:23:00.359287 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:23:00.359302 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:23:00.359315 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:23:00.359461 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:23:00.399578 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:23:00.399667 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:23:00.399775 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:23:00.399842 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:23:00.399914 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:23:00.400210 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:23:00.440548 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:23:00.440607 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:23:00.440616 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:23:00.440722 [22;34mDEBUG [0m[RTMPPUBSUB2] pubType=live - server_session.go:474
2025/07/03 08:23:00.440732 [22;36m INFO [0m[RTMPPUBSUB2] < R publish('test-stream') - server_session.go:475
2025/07/03 08:23:00.440739 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:23:00.440777 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:23:00.440795 [22;34mDEBUG [0m[RTMP2MPEGTS2] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:23:00.440803 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS2] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:23:00.440814 [22;36m INFO [0m[HLSMUXER2] lifecycle new hls muxer. muxer=0xc00024f590, streamName=test-stream - muxer.go:116
2025/07/03 08:23:00.440822 [22;36m INFO [0m[HLSMUXER2] start hls muxer. - muxer.go:121
2025/07/03 08:23:00.440976 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:23:00.440997 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:23:00.892595 [22;33m WARN [0mcancel cleanup hls file path since hls muxer still alive. streamName=test-stream - server_manager__.go:753
2025/07/03 08:23:01.091058 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:23:01.091155 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:23:01.840337 [22;34mDEBUG [0m[0xc000118870] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:01.990818 [22;34mDEBUG [0m[0xc000118870] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:02.140279 [22;34mDEBUG [0m[0xc000118870] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:02.290263 [22;34mDEBUG [0m[0xc000118870] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:02.440036 [22;34mDEBUG [0m[0xc000118870] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:02.456998 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:23:02.457086 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:23:02.457125 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:23:02.457139 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:23:02.457151 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:23:02.457161 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:23:02.457171 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:23:02.457225 [22;36m INFO [0m[HLSMUXER2] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:23:19.394522 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:23:19.394566 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:23:19.394726 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:23:19.394840 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 250, "fragment_num": 2, "delete_threshold": 2, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:250 FragmentNum:2 DeleteThreshold:2 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:23:19.394872 [22;36m INFO [0m     start: 2025-07-03 08:23:19.393 - base.go:35
2025/07/03 08:23:19.394906 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:23:19.394916 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:23:19.394930 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:23:19.394939 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:23:19.394949 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:23:19.394962 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:23:19.395207 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:23:19.395258 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:23:24.424491 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:35514 - server.go:95
2025/07/03 08:23:24.424599 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc0002b4010, naza.Connection=0xc0002d0000 - connection.go:193
2025/07/03 08:23:24.424643 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0002d6000, remote addr=127.0.0.1:35514 - server_session.go:113
2025/07/03 08:23:24.424691 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:23:24.424710 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:23:24.424727 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:23:24.424840 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:23:24.465590 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:23:24.465672 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:23:24.465721 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:23:24.465749 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:23:24.465776 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:23:24.465947 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:23:24.506581 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:23:24.506644 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:23:24.506659 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:23:24.506770 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:23:24.506790 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:23:24.506803 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:23:24.506987 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc000400008, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:23:24.507027 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:23:24.507086 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:23:24.507101 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:23:24.507121 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0002cf590, streamName=test-stream - muxer.go:116
2025/07/03 08:23:24.507137 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:23:24.507295 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:23:24.507339 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:23:25.160659 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:23:25.160789 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:23:25.160872 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:23:25.895516 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:26.044418 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:26.193405 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:26.342504 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:26.491514 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:26.640439 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:26.789928 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:27.491419 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:27.640320 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:27.691771 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=0, ts=226440, frame=sid=224, dts=226440, pts=226440, cc=145, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:27.806569 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:27.957107 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:28.106763 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:28.257088 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:29.106966 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:29.257043 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:29.704315 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:29.853004 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:30.002010 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:30.706699 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:30.856797 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:31.006517 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:31.156835 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:31.306596 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:31.456601 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:31.607091 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:31.856895 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=374940, ts=601470, frame=sid=224, dts=601470, pts=602910, cc=222, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:32.306333 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:32.456364 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:32.606696 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:33.056426 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:33.906426 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:34.056428 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:34.207095 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:34.356842 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:34.513067 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:34.662045 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:34.811106 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:35.189886 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=673470, ts=899910, frame=sid=224, dts=899910, pts=902880, cc=254, key=false, len=15496, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:35.669908 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:35.818897 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:35.967980 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:36.116622 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:36.265909 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:36.414930 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:37.712195 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:37.861141 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:38.010002 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:38.712044 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:38.860801 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:39.009654 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:39.158564 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:39.350306 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=1048410, ts=1274940, frame=sid=224, dts=1274940, pts=1282410, cc=177, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:39.457230 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:39.607594 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:40.308001 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:40.457154 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:40.607169 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:40.756833 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:40.906639 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:41.923401 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:42.520711 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:43.519837 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:43.519916 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=1423440, ts=1649970, frame=sid=224, dts=1649970, pts=1657440, cc=252, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:43.668531 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:43.817563 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:43.966353 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:44.115278 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:44.264305 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:44.413352 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:45.115676 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:45.562565 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:45.860487 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:46.009216 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:46.710979 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:46.859730 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:47.010143 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:47.159154 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:47.308035 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:47.606979 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:47.690095 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=1798470, ts=2024910, frame=sid=224, dts=2024910, pts=2024910, cc=72, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:48.308119 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:48.456932 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:48.606757 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:48.756761 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:48.907270 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:49.056843 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:49.923386 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:50.223391 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:50.372225 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:50.521095 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:50.670297 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:50.819087 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:51.521411 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:51.623734 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=2152440, ts=2378970, frame=sid=224, dts=2378970, pts=2378970, cc=0, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:51.670413 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:52.117261 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:52.265908 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:52.415034 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:53.117088 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:53.563849 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:53.861686 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:54.010716 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:54.712683 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:54.861663 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:55.776807 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=2527470, ts=2753910, frame=sid=224, dts=2753910, pts=2755350, cc=87, key=false, len=291, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:23:56.607709 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:57.907060 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:58.056624 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:58.223276 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:58.373442 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:58.522149 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:58.670965 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:58.819907 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:59.521529 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:23:59.957136 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=2902410, ts=3128940, frame=sid=224, dts=3128940, pts=3131910, cc=153, key=false, len=6983, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:00.414860 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:01.117537 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:01.266464 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:01.415427 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:01.564291 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:01.862189 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:02.011341 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:03.160996 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:03.309886 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:03.458852 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:03.608126 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:04.118846 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=3277440, ts=3503970, frame=sid=224, dts=3503970, pts=3511440, cc=228, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:04.310695 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:04.459637 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:04.608577 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:04.757600 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:04.907007 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:05.056715 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:05.206691 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:06.056835 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:06.821031 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:07.523140 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:07.671879 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:07.820706 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:07.969728 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:08.118563 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:08.267283 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:08.288684 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=3652470, ts=3878910, frame=sid=224, dts=3878910, pts=3880350, cc=48, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:08.416298 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:09.118116 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:09.267211 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:09.416042 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:09.713860 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:09.862708 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:10.714482 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:10.863458 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:11.012448 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:11.161542 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:11.310555 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:11.459326 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:11.608188 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:12.310105 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:12.456982 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=4027410, ts=4253940, frame=sid=224, dts=4253940, pts=4253940, cc=126, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:12.459056 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:12.608012 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:12.757133 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:12.906844 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:13.056618 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:13.206747 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:14.356596 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:14.523126 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:14.672118 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:14.821035 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:15.522817 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:15.671610 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:15.820521 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:15.969774 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:16.118610 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:16.223766 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=4366440, ts=4592970, frame=sid=224, dts=4592970, pts=4594410, cc=17, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:16.267555 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:16.416344 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:17.118627 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:17.267427 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:17.416652 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:17.565588 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:17.714404 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:18.714333 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:18.863361 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:19.012302 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:19.161304 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:19.310102 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:19.459330 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:19.608333 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:20.310654 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:20.374645 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=4741470, ts=4967910, frame=sid=224, dts=4967910, pts=4969350, cc=97, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:20.459808 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:20.608777 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:20.757652 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:20.906656 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:21.056437 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:21.206805 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:21.907737 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:22.356654 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:22.672377 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:22.821196 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:23.523539 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:23.672435 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:23.821121 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:23.970270 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:24.268100 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:25.119233 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:25.416744 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:25.714799 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:25.863807 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:26.012715 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:26.864700 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:26.864785 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=5324940, ts=5551470, frame=sid=224, dts=5551470, pts=5551470, cc=102, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:27.013550 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:27.162414 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:27.311386 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:27.460484 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:27.609538 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:28.312084 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:28.461023 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:28.907795 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:29.057055 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:29.207242 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:30.207394 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:30.357025 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:30.506675 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:30.656793 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:30.808898 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:31.036614 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=5699970, ts=5926410, frame=sid=224, dts=5926410, pts=5929380, cc=178, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:31.673119 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:31.822692 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:31.973227 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:32.122160 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:32.271207 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:32.419984 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:33.122272 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:33.271219 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:33.420187 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:33.556921 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=5926410, ts=6152940, frame=sid=224, dts=6152940, pts=6155910, cc=203, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:33.568835 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:33.717746 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:33.866712 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:34.015468 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:34.717692 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:34.866685 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:35.015537 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:35.164738 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:35.612148 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:36.314996 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:36.463961 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:36.613070 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:36.761660 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:36.910668 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:37.059548 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:37.208744 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:37.740423 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=6302970, ts=6529410, frame=sid=224, dts=6529410, pts=6532380, cc=57, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:37.910849 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:38.059748 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:38.208686 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:38.357344 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:38.507079 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:38.656796 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:38.806901 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:41.718759 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:41.907228 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=6677910, ts=6904440, frame=sid=224, dts=6904440, pts=6904440, cc=63, key=false, len=102, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:42.718657 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:42.867919 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:43.016746 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:43.165972 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:43.314868 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:43.463737 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:44.315663 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:44.464681 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:44.613552 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:44.762551 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:44.911608 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:45.062780 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:45.209640 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:46.082817 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:46.082901 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=7052940, ts=7279470, frame=sid=224, dts=7279470, pts=7279470, cc=194, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:46.210447 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:46.359801 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:46.509290 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:46.658088 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:47.658042 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:47.806732 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:48.406530 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:49.124805 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:49.273360 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:49.423378 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:49.572221 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:49.623809 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=7372440, ts=7598970, frame=sid=224, dts=7598970, pts=7598970, cc=223, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:49.870108 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:50.721968 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:50.870843 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:51.019576 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:51.168907 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:52.467195 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:52.764911 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:52.914052 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:53.063005 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:53.211818 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:53.786696 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=7747470, ts=7973910, frame=sid=224, dts=7973910, pts=7973910, cc=37, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:53.914389 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:54.212373 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:54.659538 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:54.808508 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:57.107693 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:57.257892 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:57.407102 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:57.556765 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:57.707104 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:57.857041 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:57.940551 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=8122410, ts=8348940, frame=sid=224, dts=8348940, pts=8348940, cc=111, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:24:58.006688 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:58.873901 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:59.022461 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:59.171772 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:59.320713 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:24:59.469620 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:00.470081 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:00.618920 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:00.767925 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:00.916607 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:01.065431 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:01.214587 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:01.918289 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:02.067698 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:02.123397 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=8497440, ts=8723970, frame=sid=224, dts=8723970, pts=8723970, cc=189, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:02.216462 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:02.365355 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:02.514415 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:02.663423 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:02.813590 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:03.517112 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:03.665979 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:03.963605 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:05.262556 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:05.560550 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:05.709810 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:05.858995 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:06.014959 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:06.285580 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=8872470, ts=9098910, frame=sid=224, dts=9098910, pts=9098910, cc=7, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:07.158743 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:07.307629 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:07.456745 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:07.607033 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:08.307334 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:08.457268 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:08.606915 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:08.756724 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:08.796640 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=9098910, ts=9325440, frame=sid=224, dts=9325440, pts=9328410, cc=31, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:08.907627 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:09.056929 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:09.221942 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:09.924162 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:10.073344 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:10.520778 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:10.669747 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:11.521000 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:11.670091 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:11.818939 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:11.967933 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:12.116848 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:12.265970 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:12.415104 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:12.649181 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=9445410, ts=9671940, frame=sid=224, dts=9671940, pts=9671940, cc=69, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:13.117346 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:13.266420 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:13.415481 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:13.564743 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:13.713554 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:13.862415 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:14.011811 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:15.161038 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:15.310011 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:15.608170 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:16.310071 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:16.459035 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:16.607812 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:16.757328 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:16.821282 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=9820440, ts=10046970, frame=sid=224, dts=10046970, pts=10054440, cc=146, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:16.906969 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:17.057369 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:17.206584 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:17.907633 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:18.056360 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:18.207617 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:18.357093 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:18.807797 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:19.524269 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:19.673157 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:19.822077 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:19.970982 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:20.119673 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:20.417584 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:20.989818 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=10195470, ts=10421910, frame=sid=224, dts=10421910, pts=10423350, cc=226, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:21.119992 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:21.418393 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:21.567463 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:21.716770 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:21.865642 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:22.014913 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:23.461761 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:23.610968 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:24.313813 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:24.462981 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:24.611719 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:24.672483 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=10526940, ts=10753470, frame=sid=224, dts=10753470, pts=10753470, cc=230, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:24.760959 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:24.909636 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:25.058539 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:25.207611 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:26.058859 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:26.207789 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:27.507323 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:27.821542 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:27.973769 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:28.123482 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:28.272075 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:28.421208 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:28.840272 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=10901970, ts=11128410, frame=sid=224, dts=11128410, pts=11128410, cc=49, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:31.166460 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:31.612881 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:32.314867 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:32.463820 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:32.612916 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:32.761829 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:32.910964 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:32.996232 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=11276910, ts=11503440, frame=sid=224, dts=11503440, pts=11503440, cc=120, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:33.059990 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:33.209006 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:33.913063 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:34.060310 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:34.209341 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:34.358454 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:34.507560 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:35.507943 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:35.656848 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:35.956955 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:36.106847 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:36.256771 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:36.406774 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:37.124158 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:37.174066 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=11651940, ts=11878470, frame=sid=224, dts=11878470, pts=11878470, cc=195, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:37.273106 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:37.422253 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:37.571212 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:37.720297 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:37.869159 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:38.719996 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:39.613650 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:40.316110 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:40.465086 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:40.614055 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:40.763219 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:40.912063 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:41.061092 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:41.210038 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:41.338005 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=12026970, ts=12253410, frame=sid=224, dts=12253410, pts=12260880, cc=12, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:41.912398 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:42.061386 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:42.210506 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:42.359584 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:42.514558 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:42.657937 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:43.957369 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:44.107400 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:44.256544 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:44.406453 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:45.107401 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:45.507283 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=12401910, ts=12628440, frame=sid=224, dts=12628440, pts=12631410, cc=86, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:45.871831 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:46.020706 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:46.722878 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:46.871576 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:47.020651 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:48.318347 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:48.467433 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:48.616403 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:48.765556 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:48.914332 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:49.063218 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:49.212194 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:49.673560 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=12776940, ts=13003470, frame=sid=224, dts=13003470, pts=13003470, cc=164, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:50.063017 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:50.212149 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:50.361170 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:50.510096 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:50.807795 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:53.107072 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:53.273824 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:53.423803 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:53.573139 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:53.722168 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:53.840424 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=13151970, ts=13378410, frame=sid=224, dts=13378410, pts=13378410, cc=244, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:53.871042 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:54.722048 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:54.871599 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:55.019802 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:55.168716 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:55.317576 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:55.466234 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:56.614873 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:56.913025 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:57.061832 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:57.210634 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:57.997809 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=13526910, ts=13753440, frame=sid=224, dts=13753440, pts=13753440, cc=60, key=false, len=4894, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:25:58.061508 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:58.210513 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:58.509430 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:58.658287 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:58.807451 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:59.509527 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:59.808682 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:25:59.957667 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:00.106919 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:01.257658 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:01.407324 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:01.557401 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:01.723263 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:02.173850 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=13901940, ts=14128470, frame=sid=224, dts=14128470, pts=14128470, cc=136, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:03.320055 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:03.618094 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:04.618267 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:04.767376 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:04.916403 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:05.214475 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:06.066228 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:06.215706 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:06.340488 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=14276970, ts=14503410, frame=sid=224, dts=14503410, pts=14504850, cc=208, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:06.364760 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:06.513696 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:06.662609 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:07.513897 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:07.960745 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:09.110333 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:09.408371 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:09.557693 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:09.707171 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:09.857280 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:10.006859 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:10.493563 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=14651910, ts=14878440, frame=sid=224, dts=14878440, pts=14878440, cc=27, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:10.709219 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:10.858307 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:11.008741 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:11.156579 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:11.306778 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:11.456581 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:11.622032 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:12.771745 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:13.069491 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:13.218375 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:13.920392 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:14.218252 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:14.366981 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:14.515968 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:14.664723 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:14.664786 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=15026940, ts=15253470, frame=sid=224, dts=15253470, pts=15256440, cc=101, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:14.813728 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:15.516057 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:15.664815 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:15.813822 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:15.962928 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:16.111746 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:17.111902 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:17.260787 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:17.409781 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:17.558695 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:17.707910 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:17.857338 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:18.708076 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:18.835803 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=15401970, ts=15628410, frame=sid=224, dts=15628410, pts=15628410, cc=177, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:18.857517 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:19.007361 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:19.157395 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:19.307494 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:19.457921 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:19.607752 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:20.324147 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:20.475245 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:21.070414 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:21.219296 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:21.921857 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:22.070954 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:22.219850 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:22.369077 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:22.517747 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:22.667110 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:22.816001 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:23.007145 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=15776910, ts=16003440, frame=sid=224, dts=16003440, pts=16003440, cc=255, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:23.519055 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:23.668460 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:23.817456 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:23.966537 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:24.115743 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:24.264578 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:24.413341 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:25.263788 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:25.412670 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:25.561504 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:25.710678 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:25.859551 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:26.008854 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:26.711292 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:26.860019 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:27.009081 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:27.158167 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:27.158233 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=16151940, ts=16378470, frame=sid=224, dts=16378470, pts=16378470, cc=76, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:27.307272 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:27.457005 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:27.607811 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:28.456914 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:28.606904 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:28.757452 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:29.222051 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:29.925156 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:30.073919 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:30.521147 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:31.339791 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=16526970, ts=16753410, frame=sid=224, dts=16753410, pts=16753410, cc=151, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:31.669197 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:31.818339 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:31.967159 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:32.116352 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:32.265532 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:32.414883 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:34.713537 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:34.862416 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:35.014583 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:35.160609 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:35.309528 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:35.501022 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=16901910, ts=17128440, frame=sid=224, dts=17128440, pts=17131410, cc=226, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:35.607144 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:36.309970 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:36.459011 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:36.608044 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:36.756958 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:36.906812 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:37.056985 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:38.057246 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:38.206906 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:38.357428 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:38.507076 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:38.672474 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:38.821694 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:39.523897 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:39.672928 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:39.673027 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=17276940, ts=17503470, frame=sid=224, dts=17503470, pts=17504910, cc=45, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:39.822093 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:39.970998 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:40.120165 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:40.269043 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:40.417876 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:41.119810 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:42.716144 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:42.865081 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:43.312005 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:43.460821 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:43.610100 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:43.652583 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=17635410, ts=17861940, frame=sid=224, dts=17861940, pts=17861940, cc=57, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:44.312539 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:44.461301 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:44.610059 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:44.759033 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:44.908284 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:45.057198 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:45.206980 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:45.908699 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:46.057336 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:46.207074 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:46.808710 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:47.523376 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:47.823016 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=18010440, ts=18236970, frame=sid=224, dts=18236970, pts=18239940, cc=167, key=false, len=525, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:47.972173 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:48.121390 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:48.270374 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:48.419381 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:49.121796 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:49.271049 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:49.420011 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:50.015660 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:50.719064 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:50.868095 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:51.016786 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:51.165859 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:51.314787 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:51.463948 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:51.613034 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:51.990295 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=18385470, ts=18611910, frame=sid=224, dts=18611910, pts=18614880, cc=205, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:52.314936 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:52.463917 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:52.612868 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:52.761685 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:52.910501 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:53.059475 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:53.208584 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:54.357635 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:54.507563 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:54.656918 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:54.806425 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:55.507417 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:55.656525 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:55.807046 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:55.957544 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:56.106987 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:56.145447 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=18760410, ts=18986940, frame=sid=224, dts=18986940, pts=18986940, cc=29, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:26:56.257115 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:56.421907 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:57.123804 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:57.272699 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:57.421731 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:57.570773 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:57.720055 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:58.719658 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:58.868518 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:59.017537 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:59.166843 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:59.315611 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:59.464612 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:26:59.613654 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:00.316298 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:00.316386 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=19135440, ts=19361970, frame=sid=224, dts=19361970, pts=19361970, cc=107, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:27:00.465192 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:00.763053 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:00.912141 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:01.061179 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:01.209937 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:01.911923 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:02.359083 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:02.657684 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:02.807250 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:03.507631 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:03.657342 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:03.807598 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:03.957791 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:04.124292 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:04.273195 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:04.424232 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:04.507096 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=19510470, ts=19736910, frame=sid=224, dts=19736910, pts=19744380, cc=187, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:27:05.124118 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:05.273337 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:05.423854 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:05.573533 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:07.323417 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:07.473709 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:07.623956 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:08.773976 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:08.923610 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:09.073263 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:09.225540 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:09.923630 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:10.072990 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:10.223026 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:10.373037 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:10.523044 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:10.573449 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=20057940, ts=20284470, frame=sid=224, dts=20284470, pts=20291940, cc=8, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:27:10.673378 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:10.823139 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:11.523810 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:11.672885 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:11.823312 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:11.973416 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:12.123850 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:13.125576 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:13.274632 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:13.423552 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:13.573219 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:13.723662 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:13.874062 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:14.023287 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:14.723810 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:14.742611 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=20432970, ts=20659410, frame=sid=224, dts=20659410, pts=20659410, cc=80, key=false, len=5214, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:27:14.873275 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:15.024719 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:15.173657 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:15.323538 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:15.473504 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:15.625923 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:16.328018 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:16.773033 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:16.922672 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:17.923769 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:18.073431 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:18.224161 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:18.373981 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:18.524156 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:18.674148 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:18.824291 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:18.907676 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=20807910, ts=21034440, frame=sid=224, dts=21034440, pts=21037410, cc=158, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:27:19.524257 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:19.674280 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:19.825091 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:19.973872 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:20.123656 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:20.273499 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:20.422809 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:21.274052 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:21.423371 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:21.723693 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:21.873335 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:22.723626 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:22.873645 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:23.024118 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:23.082811 [22;33m WARN [0m[HLSMUXER1] force fragment split. fragTs=21182940, ts=21409470, frame=sid=224, dts=21409470, pts=21416940, cc=234, key=false, len=5213, payload=00000000  00 00 00 01 09 f0 00 00                           |........|
 - muxer.go:215
2025/07/03 08:27:23.173670 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:23.323943 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:23.473829 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:23.623755 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:24.324033 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:27:24.474506 [22;34mDEBUG [0m[0xc0002aa330] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
