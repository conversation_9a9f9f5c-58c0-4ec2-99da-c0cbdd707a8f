2025/07/03 08:04:54.178526 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:04:54.178560 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:04:54.178699 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:04:54.178798 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:04:54.178836 [22;36m INFO [0m     start: 2025-07-03 08:04:54.17 - base.go:35
2025/07/03 08:04:54.178862 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:04:54.178871 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:04:54.178879 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:04:54.178885 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:04:54.178891 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:04:54.178897 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:04:54.179054 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:04:54.179226 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:05:16.480206 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:33134 - server.go:95
2025/07/03 08:05:16.480310 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000114010, naza.Connection=0xc00014a000 - connection.go:193
2025/07/03 08:05:16.480351 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc000154000, remote addr=[::1]:33134 - server_session.go:113
2025/07/03 08:05:16.480409 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:05:16.480436 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:05:16.480452 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:05:16.480549 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:05:16.521652 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:05:16.521752 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:05:16.521792 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:05:16.521819 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:05:16.521834 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:05:16.563625 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.563687 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.563707 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:05:16.563723 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:05:16.604602 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:05:16.604710 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test_stream') - server_session.go:475
2025/07/03 08:05:16.604729 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:05:16.604936 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc000384008, appName=live, streamName=test_stream - group__.go:185
2025/07/03 08:05:16.604988 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:05:16.605132 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:05:16.605181 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:05:16.605221 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc000149590, streamName=test_stream - muxer.go:116
2025/07/03 08:05:16.605243 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:05:16.634092 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=13, value=duration: 0
width: 320
height: 240
videodatarate: 0
framerate: 30
videocodecid: 7
audiodatarate: 67.3828125
audiosamplerate: 44100
audiosamplesize: 16
stereo: false
audiocodecid: 10
encoder: Lavf58.76.100
filesize: 0
 - group__core_streaming.go:190
2025/07/03 08:05:16.634199 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:321 - gop_cache.go:93
2025/07/03 08:05:16.634245 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:61 - gop_cache.go:115
2025/07/03 08:05:16.634304 [22;34mDEBUG [0msps={ProfileIdc:244 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:13 SpsId:0 ChromaFormatIdc:3 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:2 Log2MaxPicOrderCntLsb:0 NumRefFrames:1 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:19 PicHeightInMapUnitsMinusOne:14 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:0 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:0 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:05:16.634363 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:05:16.653005 [22;33m WARN [0m[RTMP2MPEGTS1] rtmp msg too short, ignore. header={Csid:6 MsgLen:5 MsgTypeId:9 MsgStreamId:1 TimestampAbs:2990}, payload=00000000  17 02 00 00 00                                    |.....|
 - rtmp2mpegts.go:193
2025/07/03 08:05:16.653043 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCUnpublish, header={Csid:3 MsgLen:38 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=23, wpos=38, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.653056 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=deleteStream, header={Csid:3 MsgLen:34 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=24, wpos=34, hex=00000000  05 00 3f f0 00 00 00 00  00 00                    |..?.......|
 - server_session.go:366
2025/07/03 08:05:16.653072 [22;34mDEBUG [0m[NAZACONN1] close once. err=EOF - connection.go:509
2025/07/03 08:05:16.653119 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=EOF - server_session.go:549
2025/07/03 08:05:16.653129 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:05:16.653138 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:05:16.653150 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:05:17.179509 [22;36m INFO [0merase inactive group. [GROUP1] - server_manager__.go:316
2025/07/03 08:05:17.179568 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:05:17.244717 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:33142 - server.go:95
2025/07/03 08:05:17.244779 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc00022a000, naza.Connection=0xc0000ca2c0 - connection.go:193
2025/07/03 08:05:17.244817 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc0001baea0, remote addr=[::1]:33142 - server_session.go:113
2025/07/03 08:05:17.244865 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:05:17.244888 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:05:17.244900 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:05:17.245041 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:05:17.286637 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:05:17.286717 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:05:17.286775 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:05:17.286846 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:05:17.286879 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:05:17.328561 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:05:17.328623 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:05:17.328640 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:05:17.369657 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:17.369724 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test_stream'). - server_session.go:509
2025/07/03 08:05:17.369782 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:05:17.369879 [22;36m INFO [0m[GROUP2] lifecycle new group. group=0xc0000dc008, appName=live, streamName=test_stream - group__.go:185
2025/07/03 08:05:17.369904 [22;34mDEBUG [0m[GROUP2] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:05:34.179723 [22;33m WARN [0m[GROUP2] session timeout. session=RTMPPUBSUB2 - group__.go:468
2025/07/03 08:05:34.179860 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:05:34.179897 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:05:34.179950 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:05:34.180113 [22;34mDEBUG [0m[GROUP2] [RTMPPUBSUB2] del rtmp SubSession from group. - group__out_sub.go:137
2025/07/03 08:05:35.180111 [22;36m INFO [0merase inactive group. [GROUP2] - server_manager__.go:316
2025/07/03 08:05:35.180176 [22;36m INFO [0m[GROUP2] lifecycle dispose group. - group__.go:222
2025/07/03 08:05:40.654540 [22;36m INFO [0mcleanup hls file path. streamName=test_stream, path=hls/lal_hls/test_stream - server_manager__.go:758
2025/07/03 08:06:18.036739 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:06:18.036788 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:06:18.036948 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:06:18.037056 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:06:18.037086 [22;36m INFO [0m     start: 2025-07-03 08:06:18.035 - base.go:35
2025/07/03 08:06:18.037112 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:06:18.037122 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:06:18.037132 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:06:18.037141 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:06:18.037151 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:06:18.037160 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:06:18.037302 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:06:18.037346 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:06:21.894288 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:41570 - server.go:95
2025/07/03 08:06:21.894335 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc00019c238, naza.Connection=0xc0003a4160 - connection.go:193
2025/07/03 08:06:21.894356 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0001b3a00, remote addr=127.0.0.1:41570 - server_session.go:113
2025/07/03 08:06:21.894378 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:06:21.894395 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:06:21.894405 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:06:21.894476 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:06:21.935615 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:06:21.935692 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:06:21.935767 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:06:21.935818 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:06:21.935863 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:06:21.935991 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:21.976554 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:21.976590 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:06:21.976598 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:06:21.976683 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:06:21.976696 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:06:21.976704 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:06:21.976777 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc0001d2e08, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:06:21.976797 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:06:21.976901 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:06:21.976917 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:06:21.976938 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003e04b0, streamName=test-stream - muxer.go:116
2025/07/03 08:06:21.976949 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:06:21.977051 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:06:21.977077 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:06:22.633513 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:06:22.633654 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:06:22.633736 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:06:26.723246 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:27.149974 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:37076 - server.go:95
2025/07/03 08:06:27.150015 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc00019c000, naza.Connection=0xc0000ca210 - connection.go:193
2025/07/03 08:06:27.150033 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc0000da000, remote addr=[::1]:37076 - server_session.go:113
2025/07/03 08:06:27.150056 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:06:27.150070 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:06:27.150077 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:06:27.150203 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:06:27.190581 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:06:27.190619 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:06:27.190645 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:06:27.190659 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:06:27.190670 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:06:27.231564 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:06:27.231642 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:06:27.231705 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:06:27.272630 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:27.272689 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test-stream'). - server_session.go:509
2025/07/03 08:06:27.272727 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:06:27.272789 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:06:27.273287 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write metadata - group__core_streaming.go:253
2025/07/03 08:06:27.273308 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write vsh - group__core_streaming.go:257
2025/07/03 08:06:27.273319 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write ash - group__core_streaming.go:261
2025/07/03 08:06:29.773239 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:32.273918 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:34.723276 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:37.923272 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:38.851286 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=2511164. - server_session.go:272
2025/07/03 08:06:43.023081 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:46.872795 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=5019216. - server_session.go:272
2025/07/03 08:06:54.873242 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=7531258. - server_session.go:272
2025/07/03 08:07:02.853326 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=10041664. - server_session.go:272
2025/07/03 08:07:05.572971 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:07:08.323620 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:07:10.854772 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=12600460. - server_session.go:272
2025/07/03 08:07:15.491538 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCUnpublish, header={Csid:3 MsgLen:38 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=23, wpos=38, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:07:15.491601 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=deleteStream, header={Csid:3 MsgLen:34 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=24, wpos=34, hex=00000000  05 00 3f f0 00 00 00 00  00 00                    |..?.......|
 - server_session.go:366
2025/07/03 08:07:15.491622 [22;34mDEBUG [0m[NAZACONN1] close once. err=EOF - connection.go:509
2025/07/03 08:07:15.491689 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=EOF - server_session.go:549
2025/07/03 08:07:15.491705 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:07:15.491721 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:07:15.491754 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:07:28.037570 [22;33m WARN [0m[GROUP1] session timeout. session=RTMPPUBSUB2 - group__.go:468
2025/07/03 08:07:28.037658 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:07:28.037678 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:07:28.037698 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:07:28.037807 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] del rtmp SubSession from group. - group__out_sub.go:137
2025/07/03 08:07:29.038113 [22;36m INFO [0merase inactive group. [GROUP1] - server_manager__.go:316
2025/07/03 08:07:29.038171 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:07:39.493983 [22;36m INFO [0mcleanup hls file path. streamName=test-stream, path=hls/lal_hls/test-stream - server_manager__.go:758
2025/07/03 08:08:15.965537 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:15.965640 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:15.965702 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:18.760713 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:18.760751 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:18.760946 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:18.761079 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:18.761126 [22;36m INFO [0m     start: 2025-07-03 08:08:18.758 - base.go:35
2025/07/03 08:08:18.761156 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:18.761169 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:18.761182 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:18.761195 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:18.761207 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:18.761219 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:18.761367 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:18.761412 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:22.112083 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:22.112184 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:22.112240 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:37.555707 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:37.555757 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:37.555925 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:37.556067 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:37.556093 [22;36m INFO [0m     start: 2025-07-03 08:08:37.554 - base.go:35
2025/07/03 08:08:37.556121 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:37.556131 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:37.556142 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:37.556152 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:37.556162 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:37.556177 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:37.556355 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:37.556397 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:39.663672 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:39.663804 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:39.663859 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:50.946965 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:50.947016 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:50.947277 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:50.947383 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:50.947419 [22;36m INFO [0m     start: 2025-07-03 08:08:50.945 - base.go:35
2025/07/03 08:08:50.947445 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:50.947455 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:50.947473 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:50.947487 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:50.947497 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:50.947506 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:50.947651 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:50.947696 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:56.157939 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:51916 - server.go:95
2025/07/03 08:08:56.157993 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc00029e018, naza.Connection=0xc0002c8000 - connection.go:193
2025/07/03 08:08:56.158019 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0002ce000, remote addr=127.0.0.1:51916 - server_session.go:113
2025/07/03 08:08:56.158038 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:08:56.158047 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:08:56.158054 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:08:56.158123 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:08:56.198572 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:08:56.198649 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:08:56.198701 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:08:56.198734 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:08:56.198765 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:08:56.198933 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:08:56.239555 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:08:56.239597 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:08:56.239605 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:08:56.239676 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:08:56.239687 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:08:56.239695 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:08:56.239781 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc000504008, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:08:56.239801 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:08:56.239845 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:08:56.239859 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:08:56.239877 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0002c7590, streamName=test-stream - muxer.go:116
2025/07/03 08:08:56.239888 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:08:56.240025 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:08:56.240053 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:08:56.891071 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:08:56.891155 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:08:56.891191 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:09:00.989302 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:01.734603 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:02.019240 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:35564 - server.go:95
2025/07/03 08:09:02.019280 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000204048, naza.Connection=0xc0002c8160 - connection.go:193
2025/07/03 08:09:02.019301 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00013fa00, remote addr=[::1]:35564 - server_session.go:113
2025/07/03 08:09:02.019324 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:09:02.019339 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:09:02.019348 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:09:02.019508 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:09:02.059593 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:09:02.059645 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:09:02.059680 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:09:02.059696 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:09:02.059710 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:09:02.100530 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:09:02.100581 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:09:02.100601 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:09:02.142570 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:09:02.142615 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test-stream'). - server_session.go:509
2025/07/03 08:09:02.142655 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:09:02.142701 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:09:02.156880 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write metadata - group__core_streaming.go:253
2025/07/03 08:09:02.156927 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write vsh - group__core_streaming.go:257
2025/07/03 08:09:02.156936 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write ash - group__core_streaming.go:261
2025/07/03 08:09:04.640239 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:07.240575 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:07.690837 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:09.585619 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:12.649683 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=2501472. - server_session.go:272
2025/07/03 08:09:12.790434 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:13.790260 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:15.989195 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:17.286833 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:20.190674 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:20.265752 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=5039141. - server_session.go:272
2025/07/03 08:09:21.640229 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:22.090337 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:23.689807 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:26.440384 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:28.672805 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=7539318. - server_session.go:272
2025/07/03 08:09:28.789755 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:29.640738 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:30.539872 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:36.606813 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=10041752. - server_session.go:272
2025/07/03 08:09:44.190389 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:44.556581 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=12542883. - server_session.go:272
2025/07/03 08:09:45.789788 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:46.390248 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:49.290778 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:52.540394 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=15046277. - server_session.go:272
2025/07/03 08:09:55.540002 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:59.039494 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:00.457122 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=17552582. - server_session.go:272
2025/07/03 08:10:02.240293 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:05.585232 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:08.173853 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=20086146. - server_session.go:272
2025/07/03 08:10:10.239856 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:11.989711 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.190662 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.340251 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.499545 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=22726201. - server_session.go:272
2025/07/03 08:10:23.190773 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:24.790660 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:24.840567 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=25328651. - server_session.go:272
2025/07/03 08:10:27.049334 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:10:27.049404 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:10:27.049437 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:10:27.049447 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:10:27.049470 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:10:27.049510 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:10:27.049523 [22;34mDEBUG [0m[NAZACONN1] close once. err=<nil> - connection.go:509
2025/07/03 08:10:27.049569 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:10:27.049582 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:10:27.049588 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:10:27.049632 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:10:27.050009 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
