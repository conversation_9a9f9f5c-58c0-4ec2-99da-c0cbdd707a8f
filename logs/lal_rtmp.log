2025/07/03 08:04:54.178526 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:04:54.178560 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:04:54.178699 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:04:54.178798 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:04:54.178836 [22;36m INFO [0m     start: 2025-07-03 08:04:54.17 - base.go:35
2025/07/03 08:04:54.178862 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:04:54.178871 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:04:54.178879 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:04:54.178885 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:04:54.178891 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:04:54.178897 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:04:54.179054 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:04:54.179226 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:05:16.480206 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:33134 - server.go:95
2025/07/03 08:05:16.480310 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000114010, naza.Connection=0xc00014a000 - connection.go:193
2025/07/03 08:05:16.480351 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc000154000, remote addr=[::1]:33134 - server_session.go:113
2025/07/03 08:05:16.480409 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:05:16.480436 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:05:16.480452 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:05:16.480549 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:05:16.521652 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:05:16.521752 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:05:16.521792 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:05:16.521819 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:05:16.521834 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:05:16.563625 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.563687 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.563707 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:05:16.563723 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:05:16.604602 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:05:16.604710 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test_stream') - server_session.go:475
2025/07/03 08:05:16.604729 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:05:16.604936 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc000384008, appName=live, streamName=test_stream - group__.go:185
2025/07/03 08:05:16.604988 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:05:16.605132 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:05:16.605181 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:05:16.605221 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc000149590, streamName=test_stream - muxer.go:116
2025/07/03 08:05:16.605243 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:05:16.634092 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=13, value=duration: 0
width: 320
height: 240
videodatarate: 0
framerate: 30
videocodecid: 7
audiodatarate: 67.3828125
audiosamplerate: 44100
audiosamplesize: 16
stereo: false
audiocodecid: 10
encoder: Lavf58.76.100
filesize: 0
 - group__core_streaming.go:190
2025/07/03 08:05:16.634199 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:321 - gop_cache.go:93
2025/07/03 08:05:16.634245 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:61 - gop_cache.go:115
2025/07/03 08:05:16.634304 [22;34mDEBUG [0msps={ProfileIdc:244 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:13 SpsId:0 ChromaFormatIdc:3 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:2 Log2MaxPicOrderCntLsb:0 NumRefFrames:1 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:19 PicHeightInMapUnitsMinusOne:14 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:0 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:0 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:05:16.634363 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:05:16.653005 [22;33m WARN [0m[RTMP2MPEGTS1] rtmp msg too short, ignore. header={Csid:6 MsgLen:5 MsgTypeId:9 MsgStreamId:1 TimestampAbs:2990}, payload=00000000  17 02 00 00 00                                    |.....|
 - rtmp2mpegts.go:193
2025/07/03 08:05:16.653043 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCUnpublish, header={Csid:3 MsgLen:38 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=23, wpos=38, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:16.653056 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=deleteStream, header={Csid:3 MsgLen:34 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=24, wpos=34, hex=00000000  05 00 3f f0 00 00 00 00  00 00                    |..?.......|
 - server_session.go:366
2025/07/03 08:05:16.653072 [22;34mDEBUG [0m[NAZACONN1] close once. err=EOF - connection.go:509
2025/07/03 08:05:16.653119 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=EOF - server_session.go:549
2025/07/03 08:05:16.653129 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:05:16.653138 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:05:16.653150 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:05:17.179509 [22;36m INFO [0merase inactive group. [GROUP1] - server_manager__.go:316
2025/07/03 08:05:17.179568 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:05:17.244717 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:33142 - server.go:95
2025/07/03 08:05:17.244779 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc00022a000, naza.Connection=0xc0000ca2c0 - connection.go:193
2025/07/03 08:05:17.244817 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc0001baea0, remote addr=[::1]:33142 - server_session.go:113
2025/07/03 08:05:17.244865 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:05:17.244888 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:05:17.244900 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:05:17.245041 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:05:17.286637 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:05:17.286717 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:05:17.286775 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:05:17.286846 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:05:17.286879 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:05:17.328561 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:05:17.328623 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:05:17.328640 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:05:17.369657 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  5f 73 74 72 65 61 6d     |....test_stream|
 - server_session.go:366
2025/07/03 08:05:17.369724 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test_stream'). - server_session.go:509
2025/07/03 08:05:17.369782 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:05:17.369879 [22;36m INFO [0m[GROUP2] lifecycle new group. group=0xc0000dc008, appName=live, streamName=test_stream - group__.go:185
2025/07/03 08:05:17.369904 [22;34mDEBUG [0m[GROUP2] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:05:34.179723 [22;33m WARN [0m[GROUP2] session timeout. session=RTMPPUBSUB2 - group__.go:468
2025/07/03 08:05:34.179860 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:05:34.179897 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:05:34.179950 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:05:34.180113 [22;34mDEBUG [0m[GROUP2] [RTMPPUBSUB2] del rtmp SubSession from group. - group__out_sub.go:137
2025/07/03 08:05:35.180111 [22;36m INFO [0merase inactive group. [GROUP2] - server_manager__.go:316
2025/07/03 08:05:35.180176 [22;36m INFO [0m[GROUP2] lifecycle dispose group. - group__.go:222
2025/07/03 08:05:40.654540 [22;36m INFO [0mcleanup hls file path. streamName=test_stream, path=hls/lal_hls/test_stream - server_manager__.go:758
2025/07/03 08:06:18.036739 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:06:18.036788 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:06:18.036948 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:06:18.037056 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:06:18.037086 [22;36m INFO [0m     start: 2025-07-03 08:06:18.035 - base.go:35
2025/07/03 08:06:18.037112 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:06:18.037122 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:06:18.037132 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:06:18.037141 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:06:18.037151 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:06:18.037160 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:06:18.037302 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:06:18.037346 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:06:21.894288 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:41570 - server.go:95
2025/07/03 08:06:21.894335 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc00019c238, naza.Connection=0xc0003a4160 - connection.go:193
2025/07/03 08:06:21.894356 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0001b3a00, remote addr=127.0.0.1:41570 - server_session.go:113
2025/07/03 08:06:21.894378 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:06:21.894395 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:06:21.894405 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:06:21.894476 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:06:21.935615 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:06:21.935692 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:06:21.935767 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:06:21.935818 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:06:21.935863 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:06:21.935991 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:21.976554 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:21.976590 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:06:21.976598 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:06:21.976683 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:06:21.976696 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:06:21.976704 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:06:21.976777 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc0001d2e08, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:06:21.976797 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:06:21.976901 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:06:21.976917 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:06:21.976938 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003e04b0, streamName=test-stream - muxer.go:116
2025/07/03 08:06:21.976949 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:06:21.977051 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:06:21.977077 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:06:22.633513 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:06:22.633654 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:06:22.633736 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:06:26.723246 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:27.149974 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:37076 - server.go:95
2025/07/03 08:06:27.150015 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc00019c000, naza.Connection=0xc0000ca210 - connection.go:193
2025/07/03 08:06:27.150033 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc0000da000, remote addr=[::1]:37076 - server_session.go:113
2025/07/03 08:06:27.150056 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:06:27.150070 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:06:27.150077 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:06:27.150203 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:06:27.190581 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:06:27.190619 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:06:27.190645 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:06:27.190659 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:06:27.190670 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:06:27.231564 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:06:27.231642 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:06:27.231705 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:06:27.272630 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:06:27.272689 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test-stream'). - server_session.go:509
2025/07/03 08:06:27.272727 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:06:27.272789 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:06:27.273287 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write metadata - group__core_streaming.go:253
2025/07/03 08:06:27.273308 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write vsh - group__core_streaming.go:257
2025/07/03 08:06:27.273319 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write ash - group__core_streaming.go:261
2025/07/03 08:06:29.773239 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:32.273918 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:34.723276 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:37.923272 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:38.851286 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=2511164. - server_session.go:272
2025/07/03 08:06:43.023081 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:06:46.872795 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=5019216. - server_session.go:272
2025/07/03 08:06:54.873242 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=7531258. - server_session.go:272
2025/07/03 08:07:02.853326 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=10041664. - server_session.go:272
2025/07/03 08:07:05.572971 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:07:08.323620 [22;34mDEBUG [0m[0xc0001d92c0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:07:10.854772 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=12600460. - server_session.go:272
2025/07/03 08:07:15.491538 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCUnpublish, header={Csid:3 MsgLen:38 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=23, wpos=38, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:07:15.491601 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=deleteStream, header={Csid:3 MsgLen:34 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=24, wpos=34, hex=00000000  05 00 3f f0 00 00 00 00  00 00                    |..?.......|
 - server_session.go:366
2025/07/03 08:07:15.491622 [22;34mDEBUG [0m[NAZACONN1] close once. err=EOF - connection.go:509
2025/07/03 08:07:15.491689 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=EOF - server_session.go:549
2025/07/03 08:07:15.491705 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:07:15.491721 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:07:15.491754 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:07:28.037570 [22;33m WARN [0m[GROUP1] session timeout. session=RTMPPUBSUB2 - group__.go:468
2025/07/03 08:07:28.037658 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:07:28.037678 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:07:28.037698 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:07:28.037807 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] del rtmp SubSession from group. - group__out_sub.go:137
2025/07/03 08:07:29.038113 [22;36m INFO [0merase inactive group. [GROUP1] - server_manager__.go:316
2025/07/03 08:07:29.038171 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:07:39.493983 [22;36m INFO [0mcleanup hls file path. streamName=test-stream, path=hls/lal_hls/test-stream - server_manager__.go:758
2025/07/03 08:08:15.965537 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:15.965640 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:15.965702 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:18.760713 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:18.760751 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:18.760946 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:18.761079 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:18.761126 [22;36m INFO [0m     start: 2025-07-03 08:08:18.758 - base.go:35
2025/07/03 08:08:18.761156 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:18.761169 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:18.761182 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:18.761195 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:18.761207 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:18.761219 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:18.761367 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:18.761412 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:22.112083 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:22.112184 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:22.112240 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:37.555707 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:37.555757 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:37.555925 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:37.556067 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:37.556093 [22;36m INFO [0m     start: 2025-07-03 08:08:37.554 - base.go:35
2025/07/03 08:08:37.556121 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:37.556131 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:37.556142 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:37.556152 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:37.556162 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:37.556177 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:37.556355 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:37.556397 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:39.663672 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:08:39.663804 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:08:39.663859 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:08:50.946965 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:08:50.947016 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:08:50.947277 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:08:50.947383 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:08:50.947419 [22;36m INFO [0m     start: 2025-07-03 08:08:50.945 - base.go:35
2025/07/03 08:08:50.947445 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:08:50.947455 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:08:50.947473 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:08:50.947487 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:08:50.947497 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:08:50.947506 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:08:50.947651 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:08:50.947696 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:08:56.157939 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:51916 - server.go:95
2025/07/03 08:08:56.157993 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc00029e018, naza.Connection=0xc0002c8000 - connection.go:193
2025/07/03 08:08:56.158019 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc0002ce000, remote addr=127.0.0.1:51916 - server_session.go:113
2025/07/03 08:08:56.158038 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:08:56.158047 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:08:56.158054 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:08:56.158123 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:08:56.198572 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:08:56.198649 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:08:56.198701 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:08:56.198734 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:08:56.198765 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:08:56.198933 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:08:56.239555 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:08:56.239597 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:08:56.239605 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:08:56.239676 [22;34mDEBUG [0m[RTMPPUBSUB1] pubType=live - server_session.go:474
2025/07/03 08:08:56.239687 [22;36m INFO [0m[RTMPPUBSUB1] < R publish('test-stream') - server_session.go:475
2025/07/03 08:08:56.239695 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:08:56.239781 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc000504008, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:08:56.239801 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:08:56.239845 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:08:56.239859 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:08:56.239877 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0002c7590, streamName=test-stream - muxer.go:116
2025/07/03 08:08:56.239888 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:08:56.240025 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:08:56.240053 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:08:56.891071 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:08:56.891155 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:08:56.891191 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:09:00.989302 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:01.734603 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:02.019240 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:35564 - server.go:95
2025/07/03 08:09:02.019280 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000204048, naza.Connection=0xc0002c8160 - connection.go:193
2025/07/03 08:09:02.019301 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00013fa00, remote addr=[::1]:35564 - server_session.go:113
2025/07/03 08:09:02.019324 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:09:02.019339 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:09:02.019348 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:09:02.019508 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:09:02.059593 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:09:02.059645 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:09:02.059680 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:09:02.059696 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:09:02.059710 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:09:02.100530 [22;36m INFO [0m[RTMPPUBSUB2] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:09:02.100581 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:09:02.100601 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:09:02.142570 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:09:02.142615 [22;36m INFO [0m[RTMPPUBSUB2] < R play('test-stream'). - server_session.go:509
2025/07/03 08:09:02.142655 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:09:02.142701 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:09:02.156880 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write metadata - group__core_streaming.go:253
2025/07/03 08:09:02.156927 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write vsh - group__core_streaming.go:257
2025/07/03 08:09:02.156936 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] write ash - group__core_streaming.go:261
2025/07/03 08:09:04.640239 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:07.240575 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:07.690837 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:09.585619 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:12.649683 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=2501472. - server_session.go:272
2025/07/03 08:09:12.790434 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:13.790260 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:15.989195 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:17.286833 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:20.190674 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:20.265752 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=5039141. - server_session.go:272
2025/07/03 08:09:21.640229 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:22.090337 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:23.689807 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:26.440384 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:28.672805 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=7539318. - server_session.go:272
2025/07/03 08:09:28.789755 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:29.640738 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:30.539872 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:36.606813 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=10041752. - server_session.go:272
2025/07/03 08:09:44.190389 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:44.556581 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=12542883. - server_session.go:272
2025/07/03 08:09:45.789788 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:46.390248 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:49.290778 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:52.540394 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=15046277. - server_session.go:272
2025/07/03 08:09:55.540002 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:09:59.039494 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:00.457122 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=17552582. - server_session.go:272
2025/07/03 08:10:02.240293 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:05.585232 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:08.173853 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=20086146. - server_session.go:272
2025/07/03 08:10:10.239856 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:11.989711 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.190662 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.340251 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:16.499545 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=22726201. - server_session.go:272
2025/07/03 08:10:23.190773 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:24.790660 [22;34mDEBUG [0m[0xc0002904e0] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:10:24.840567 [22;36m INFO [0m[RTMPPUBSUB2] < R Acknowledgement. ignore. sequence number=25328651. - server_session.go:272
2025/07/03 08:10:27.049334 [22;34mDEBUG [0mdispose server manager. - server_manager__.go:356
2025/07/03 08:10:27.049404 [22;31mERROR [0maccept tcp [::]:1935: use of closed network connection - server_manager__.go:236
2025/07/03 08:10:27.049437 [22;31mERROR [0mhttp: Server closed - server_manager__.go:225
2025/07/03 08:10:27.049447 [22;36m INFO [0m[GROUP1] lifecycle dispose group. - group__.go:222
2025/07/03 08:10:27.049470 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:10:27.049510 [22;34mDEBUG [0m[NAZACONN1] Close. - connection.go:381
2025/07/03 08:10:27.049523 [22;34mDEBUG [0m[NAZACONN1] close once. err=<nil> - connection.go:509
2025/07/03 08:10:27.049569 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle dispose rtmp ServerSession. err=<nil> - server_session.go:549
2025/07/03 08:10:27.049582 [22;34mDEBUG [0m[NAZACONN2] Close. - connection.go:381
2025/07/03 08:10:27.049588 [22;34mDEBUG [0m[NAZACONN2] close once. err=<nil> - connection.go:509
2025/07/03 08:10:27.049632 [22;36m INFO [0m[HLSMUXER1] lifecycle dispose hls muxer. - muxer.go:126
2025/07/03 08:10:27.050009 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] del rtmp PubSession from group. - group__in.go:318
2025/07/03 08:13:12.507568 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:13:12.507597 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:13:12.507714 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:13:12.507798 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 2000, "fragment_num": 6, "delete_threshold": 6, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:2000 FragmentNum:6 DeleteThreshold:6 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:13:12.507822 [22;36m INFO [0m     start: 2025-07-03 08:13:12.506 - base.go:35
2025/07/03 08:13:12.507841 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:13:12.507847 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:13:12.507854 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:13:12.507860 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:13:12.507867 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:13:12.507872 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:13:12.507968 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:13:12.507996 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:13:15.590894 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:59712 - server.go:95
2025/07/03 08:13:15.590985 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000128238, naza.Connection=0xc0003aa160 - connection.go:193
2025/07/03 08:13:15.591021 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc00013fa00, remote addr=[::1]:59712 - server_session.go:113
2025/07/03 08:13:15.591093 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:13:15.591137 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:13:15.591152 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:13:15.591311 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:13:15.631606 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:13:15.631684 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:13:15.631744 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:13:15.631780 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:13:15.631841 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:13:15.672549 [22;36m INFO [0m[RTMPPUBSUB1] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:13:15.672599 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:13:15.672610 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:13:15.713617 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:13:15.713682 [22;36m INFO [0m[RTMPPUBSUB1] < R play('test-stream'). - server_session.go:509
2025/07/03 08:13:15.713750 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:13:15.713881 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc00015ae08, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:13:15.713915 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:13:20.396527 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:33604 - server.go:95
2025/07/03 08:13:20.396589 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000128248, naza.Connection=0xc0003aa370 - connection.go:193
2025/07/03 08:13:20.396621 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00013fba0, remote addr=127.0.0.1:33604 - server_session.go:113
2025/07/03 08:13:20.396650 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:13:20.396724 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:13:20.396749 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:13:20.396930 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:13:20.438587 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:13:20.438656 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:13:20.438714 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:13:20.438760 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:13:20.438789 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:13:20.438943 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:13:20.479585 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:13:20.479655 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:13:20.479671 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:13:20.479786 [22;34mDEBUG [0m[RTMPPUBSUB2] pubType=live - server_session.go:474
2025/07/03 08:13:20.479806 [22;36m INFO [0m[RTMPPUBSUB2] < R publish('test-stream') - server_session.go:475
2025/07/03 08:13:20.479820 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:13:20.479921 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:13:20.480018 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:13:20.480051 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:13:20.480074 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003ed680, streamName=test-stream - muxer.go:116
2025/07/03 08:13:20.480114 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:13:20.480179 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:13:20.480235 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:13:21.124261 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:13:21.124337 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:13:21.124387 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:13:29.173658 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:29.181953 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=2502419. - server_session.go:272
2025/07/03 08:13:34.674829 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.274511 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.574208 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.723282 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:36.874273 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:37.023573 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:37.139927 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=5003055. - server_session.go:272
2025/07/03 08:13:37.173602 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:38.624456 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:38.774392 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.473591 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.623888 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.773962 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:39.923987 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:40.073561 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:40.224039 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:40.374035 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.073199 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.223443 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.373511 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.523559 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:41.672432 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:42.674388 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:42.824202 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:42.974549 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.124182 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.273168 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.423555 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:43.573378 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.274128 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.423254 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.573566 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.723656 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:44.874053 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:45.023541 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:45.090494 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=7503915. - server_session.go:272
2025/07/03 08:13:45.173652 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:45.873713 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:46.624120 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:46.773740 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.472968 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.623194 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.773229 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:47.923191 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:48.073110 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:48.223357 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:48.373330 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.073652 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.223329 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.373315 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.523279 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.674158 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.823393 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:49.973164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:50.823715 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:50.973689 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.122898 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.271978 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.421065 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:51.570054 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.273529 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.423311 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.573693 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.723610 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:52.873676 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:53.023290 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:53.038526 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=10004752. - server_session.go:272
2025/07/03 08:13:53.174078 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:53.873059 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:54.023441 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:54.173951 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.472907 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.623155 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.774125 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:55.924114 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:56.073643 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:56.223197 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:56.373362 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.073751 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.223334 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.373772 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.523607 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.673828 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.823553 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:57.973931 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.123725 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.273679 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.423579 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:13:59.573506 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.274824 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.422985 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.573443 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.723136 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.872152 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:00.956719 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=12550224. - server_session.go:272
2025/07/03 08:14:02.173235 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:03.623536 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:03.773510 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:03.923572 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:04.073445 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:04.223346 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:04.373764 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.073586 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.223355 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.373569 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.523494 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.673554 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.823279 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:05.973594 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.122947 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.273089 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.423143 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:07.573217 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.272815 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.423026 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.572905 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.722967 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:08.873172 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:09.024077 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:09.088545 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=15053585. - server_session.go:272
2025/07/03 08:14:09.173577 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:09.873590 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.023256 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.173653 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.322884 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.472116 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:10.620990 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.473494 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.623164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.773619 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:11.923354 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:12.073531 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:12.223649 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:12.373164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.073001 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.223162 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.373145 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.523025 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.673123 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.823282 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:13.973219 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:14.673039 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:14.823521 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:15.573621 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.273678 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.423614 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.573392 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.723314 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:16.873567 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:17.024457 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:17.026224 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=17554451. - server_session.go:272
2025/07/03 08:14:17.173691 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:17.873816 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.023308 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.173461 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.323213 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.473334 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.622107 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:18.771256 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:19.622521 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:19.772912 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:19.922927 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:20.222785 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:20.373055 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.073533 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.223891 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.373559 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.523216 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.673540 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.823677 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:21.973786 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:22.673564 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:22.823435 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:22.973461 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:23.123892 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.273164 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.423051 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.573406 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.723186 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.873408 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:24.984150 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=20055309. - server_session.go:272
2025/07/03 08:14:25.023959 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:25.173942 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:25.873530 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.023636 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.173529 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.323682 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.473387 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.622929 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:26.771921 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:28.073542 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:28.223376 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:28.373667 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.073355 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.223361 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.373146 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.523236 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.673399 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.823206 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:29.973561 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:30.823197 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:30.973567 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:31.124234 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:31.273319 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:31.423148 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:32.906268 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=22561712. - server_session.go:272
2025/07/03 08:14:34.325235 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:34.474295 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:34.623639 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:35.474694 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:35.623569 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:36.073228 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:36.223258 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.090495 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.239507 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.389344 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.538515 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.687119 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.835611 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:37.984503 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:38.687342 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:38.837090 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:38.986002 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.134995 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.283948 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.433254 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:39.581852 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.475840 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.589655 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.739907 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.890258 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:40.922529 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=25062182. - server_session.go:272
2025/07/03 08:14:41.040391 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:41.189968 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:41.889777 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.040298 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.189571 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.340069 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.489794 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.640029 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:42.789738 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.490031 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.639845 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.790239 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:43.940252 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.091997 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.241050 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.390280 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.540115 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.690432 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.839896 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:45.990348 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:46.691028 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:46.840072 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:46.990030 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.139764 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.289548 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.440049 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:47.590143 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:48.873395 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=27563060. - server_session.go:272
2025/07/03 08:14:48.889960 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:49.890407 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.040159 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.190523 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.340094 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.490331 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.640025 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:50.790087 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.490332 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.639983 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.790188 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:51.940086 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:52.090604 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:52.240453 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:53.990008 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:56.440192 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:56.573715 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=30064399. - server_session.go:272
2025/07/03 08:14:56.890362 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:57.890259 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.040050 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.190562 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.340410 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.490320 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.640181 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:58.790349 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.490222 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.640175 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.789950 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:14:59.940045 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:00.090763 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:00.240664 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:00.389748 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.240104 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.390582 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.540221 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.689792 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.840030 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:01.989907 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:02.692737 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:02.841618 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:02.990662 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.140309 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.290092 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.440097 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:03.590369 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.290352 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.439808 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.589661 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.740105 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:04.773502 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=32564658. - server_session.go:272
2025/07/03 08:15:05.723034 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:05.890123 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.040366 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.189841 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.339917 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.489840 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.639836 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:06.789788 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:07.490232 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:07.639777 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:07.791238 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:09.839712 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:09.990566 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:10.991676 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.140595 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.291261 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.440149 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:11.591413 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:12.441056 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:12.723302 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=35065433. - server_session.go:272
2025/07/03 08:15:13.040034 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:13.190241 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:13.890196 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.040055 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.190539 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.340452 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.491050 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.639942 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:14.790457 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.489935 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.640131 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.790338 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:15.940356 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:16.090271 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:16.271824 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.106672 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.256709 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.406935 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.557077 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.706374 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:17.856926 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:18.007195 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:18.714617 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:18.863268 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.012079 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.161105 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.320405 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.458720 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:15:19.607588 [22;34mDEBUG [0m[0xc000161500] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:31.278056 [22;36m INFO [0minitial log succ. - config.go:249
2025/07/03 08:16:31.278129 [22;36m INFO [0m
    __    ___    __
   / /   /   |  / /
  / /   / /| | / /
 / /___/ ___ |/ /___
/_____/_/  |_/_____/
 - config.go:252
2025/07/03 08:16:31.278318 [22;33m WARN [0mconfig some fields do not exist which have been set to the zero value. fields=[rtmp.rtmps_enable rtmp.rtmps_addr rtmp.rtmps_cert_file rtmp.rtmps_key_file rtmp.gop_num rtmp.single_gop_max_frame_num rtmp.merge_write_size in_session.add_dummy_audio_enable in_session.add_dummy_audio_wait_audio_ms httpflv.enable httpflv.enable_https httpflv.url_pattern httpflv.gop_num httpflv.single_gop_max_frame_num hls.enable_https hls.sub_session_timeout_ms hls.sub_session_hash_key httpts.enable httpts.enable_https httpts.url_pattern httpts.gop_num httpts.single_gop_max_frame_num rtsp.enable rtsp.addr rtsp.rtsps_enable rtsp.rtsps_addr rtsp.rtsps_cert_file rtsp.rtsps_key_file rtsp.out_wait_key_frame_flag rtsp.ws_rtsp_enable rtsp.ws_rtsp_addr rtsp.auth_enable rtsp.auth_method rtsp.username rtsp.password record.enable_flv record.flv_out_path record.enable_mpegts record.mpegts_out_path relay_push.enable relay_push.addr_list static_relay_pull.enable static_relay_pull.addr server_id http_notify.enable http_notify.update_interval_sec http_notify.on_server_start http_notify.on_update http_notify.on_pub_start http_notify.on_pub_stop http_notify.on_sub_start http_notify.on_sub_stop http_notify.on_relay_pull_start http_notify.on_relay_pull_stop http_notify.on_rtmp_connect http_notify.on_hls_make_ts simple_auth.key simple_auth.dangerous_lal_secret simple_auth.pub_rtmp_enable simple_auth.sub_rtmp_enable simple_auth.sub_httpflv_enable simple_auth.sub_httpts_enable simple_auth.pub_rtsp_enable simple_auth.sub_rtsp_enable simple_auth.hls_m3u8_enable pprof.enable pprof.addr debug.log_group_interval_sec debug.log_group_max_group_num debug.log_group_max_sub_num_per_group] - config.go:278
2025/07/03 08:16:31.278428 [22;36m INFO [0mload conf succ. raw content={ "conf_version": "v0.4.1", "rtmp": { "enable": true, "addr": ":1935" }, "default_http": { "http_listen_addr": ":0" }, "hls": { "enable": true, "url_pattern": "/hls/", "out_path": "hls/lal_hls", "fragment_duration_ms": 500, "fragment_num": 3, "delete_threshold": 3, "cleanup_mode": 1, "use_memory_as_disk_flag": false }, "http_api": { "enable": false, "addr": ":0" }, "log": { "level": 1, "filename": "./logs/lal_rtmp.log", "is_to_stdout": true, "is_rotate_daily": true, "short_file_flag": true, "timestamp_flag": true, "timestamp_with_ms_flag": true, "level_flag": true, "assert_behavior": 1 } } parsed=&{ConfVersion:v0.4.1 RtmpConfig:{Enable:true Addr::1935 RtmpsEnable:false RtmpsAddr: RtmpsCertFile: RtmpsKeyFile: GopNum:0 SingleGopMaxFrameNum:0 MergeWriteSize:0} InSessionConfig:{AddDummyAudioEnable:false AddDummyAudioWaitAudioMs:0} DefaultHttpConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:}} HttpflvConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} HlsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:true EnableHttps:false UrlPattern:/hls/} UseMemoryAsDiskFlag:false MuxerConfig:{OutPath:hls/lal_hls FragmentDurationMs:500 FragmentNum:3 DeleteThreshold:3 CleanupMode:1} SubSessionTimeoutMs:0 SubSessionHashKey:} HttptsConfig:{CommonHttpServerConfig:{CommonHttpAddrConfig:{HttpListenAddr::0 HttpsListenAddr: HttpsCertFile: HttpsKeyFile:} Enable:false EnableHttps:false UrlPattern:} GopNum:0 SingleGopMaxFrameNum:0} RtspConfig:{Enable:false Addr: RtspsEnable:false RtspsAddr: RtspsCertFile: RtspsKeyFile: OutWaitKeyFrameFlag:false WsRtspEnable:false WsRtspAddr: ServerAuthConfig:{AuthEnable:false AuthMethod:0 UserName: PassWord:}} RecordConfig:{EnableFlv:false FlvOutPath: EnableMpegts:false MpegtsOutPath:} RelayPushConfig:{Enable:false AddrList:[]} StaticRelayPullConfig:{Enable:false Addr:} HttpApiConfig:{Enable:false Addr::0} ServerId: HttpNotifyConfig:{Enable:false UpdateIntervalSec:0 OnServerStart: OnUpdate: OnPubStart: OnPubStop: OnSubStart: OnSubStop: OnRelayPullStart: OnRelayPullStop: OnRtmpConnect: OnHlsMakeTs:} SimpleAuthConfig:{Key: DangerousLalSecret: PubRtmpEnable:false SubRtmpEnable:false SubHttpflvEnable:false SubHttptsEnable:false PubRtspEnable:false SubRtspEnable:false HlsM3u8Enable:false} PprofConfig:{Enable:false Addr:} LogConfig:{Level:1 Filename:./logs/lal_rtmp.log IsToStdout:true IsRotateDaily:true IsRotateHourly:false ShortFileFlag:true TimestampFlag:true TimestampWithMsFlag:true LevelFlag:true AssertBehavior:1 HookBackendOutFn:<nil>} DebugConfig:{LogGroupIntervalSec:0 LogGroupMaxGroupNum:0 LogGroupMaxSubNumPerGroup:0}} - config.go:346
2025/07/03 08:16:31.278451 [22;36m INFO [0m     start: 2025-07-03 08:16:31.275 - base.go:35
2025/07/03 08:16:31.278479 [22;36m INFO [0m        wd: /home/<USER>/Documents/go-streamers/go-webrtc-streamer - base.go:36
2025/07/03 08:16:31.278491 [22;36m INFO [0m      args: ./live-streaming - base.go:37
2025/07/03 08:16:31.278502 [22;36m INFO [0m   bininfo: GitTag=unknown. GitCommitLog=unknown. GitStatus=unknown. BuildTime=unknown. GoVersion=unknown. runtime=linux/amd64. - base.go:38
2025/07/03 08:16:31.278515 [22;36m INFO [0m   version: lal v0.37.4 (github.com/q191201771/lal) - base.go:39
2025/07/03 08:16:31.278524 [22;36m INFO [0m    github: https://github.com/q191201771/lal - base.go:40
2025/07/03 08:16:31.278533 [22;36m INFO [0m       doc: https://pengrl.com/lal - base.go:41
2025/07/03 08:16:31.278668 [22;36m INFO [0madd http listen for hls. addr=:0, pattern=/hls/ - server_manager__.go:195
2025/07/03 08:16:31.278699 [22;36m INFO [0mstart rtmp server listen. addr=:1935 - server.go:56
2025/07/03 08:16:34.367188 [22;36m INFO [0maccept a rtmp connection. remoteAddr=[::1]:37168 - server.go:95
2025/07/03 08:16:34.367256 [22;34mDEBUG [0m[NAZACONN1] lifecycle new connection. net.Conn=0xc000216260, naza.Connection=0xc0003a0160 - connection.go:193
2025/07/03 08:16:34.367274 [22;36m INFO [0m[RTMPPUBSUB1] lifecycle new rtmp ServerSession. session=0xc00022da00, remote addr=[::1]:37168 - server_session.go:113
2025/07/03 08:16:34.367299 [22;34mDEBUG [0mhandshake complex mode. - handshake.go:248
2025/07/03 08:16:34.367313 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:16:34.367320 [22;36m INFO [0m[RTMPPUBSUB1] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:16:34.367434 [22;36m INFO [0m[RTMPPUBSUB1] < R Handshake C2. - server_session.go:207
2025/07/03 08:16:34.407567 [22;36m INFO [0m[RTMPPUBSUB1] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:16:34.407646 [22;36m INFO [0m[RTMPPUBSUB1] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:16:34.407689 [22;36m INFO [0m[RTMPPUBSUB1] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:16:34.407709 [22;36m INFO [0m[RTMPPUBSUB1] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:16:34.407726 [22;36m INFO [0m[RTMPPUBSUB1] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:16:34.448568 [22;36m INFO [0m[RTMPPUBSUB1] < R Window Acknowledgement Size: 5000000 - server_session.go:262
2025/07/03 08:16:34.448632 [22;36m INFO [0m[RTMPPUBSUB1] < R createStream(). - server_session.go:444
2025/07/03 08:16:34.448650 [22;36m INFO [0m[RTMPPUBSUB1] > W _result(). - server_session.go:445
2025/07/03 08:16:34.489606 [22;34mDEBUG [0m[RTMPPUBSUB1] read command message, ignore it. cmd=getStreamLength, header={Csid:8 MsgLen:42 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=4096, rpos=27, wpos=42, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:16:34.489671 [22;36m INFO [0m[RTMPPUBSUB1] < R play('test-stream'). - server_session.go:509
2025/07/03 08:16:34.489711 [22;36m INFO [0m[RTMPPUBSUB1] > W onStatus('NetStream.Play.Start'). - server_session.go:519
2025/07/03 08:16:34.489778 [22;36m INFO [0m[GROUP1] lifecycle new group. group=0xc00024ee08, appName=live, streamName=test-stream - group__.go:185
2025/07/03 08:16:34.489793 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB1] add SubSession into group. - group__out_sub.go:20
2025/07/03 08:16:37.262179 [22;36m INFO [0maccept a rtmp connection. remoteAddr=127.0.0.1:57524 - server.go:95
2025/07/03 08:16:37.262239 [22;34mDEBUG [0m[NAZACONN2] lifecycle new connection. net.Conn=0xc000412000, naza.Connection=0xc000422000 - connection.go:193
2025/07/03 08:16:37.262273 [22;36m INFO [0m[RTMPPUBSUB2] lifecycle new rtmp ServerSession. session=0xc00042c000, remote addr=127.0.0.1:57524 - server_session.go:113
2025/07/03 08:16:37.262297 [22;34mDEBUG [0mhandshake simple mode. - handshake.go:236
2025/07/03 08:16:37.262313 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C0+C1. - server_session.go:197
2025/07/03 08:16:37.262323 [22;36m INFO [0m[RTMPPUBSUB2] > W Handshake S0+S1+S2. - server_session.go:199
2025/07/03 08:16:37.262404 [22;36m INFO [0m[RTMPPUBSUB2] < R Handshake C2. - server_session.go:207
2025/07/03 08:16:37.302579 [22;36m INFO [0m[RTMPPUBSUB2] < R connect('live'). tcUrl=rtmp://localhost:1935/live - server_session.go:413
2025/07/03 08:16:37.302641 [22;36m INFO [0m[RTMPPUBSUB2] > W Window Acknowledgement Size 5000000. - server_session.go:417
2025/07/03 08:16:37.302673 [22;36m INFO [0m[RTMPPUBSUB2] > W Set Peer Bandwidth. - server_session.go:422
2025/07/03 08:16:37.302689 [22;36m INFO [0m[RTMPPUBSUB2] > W SetChunkSize 4096. - server_session.go:427
2025/07/03 08:16:37.302710 [22;36m INFO [0m[RTMPPUBSUB2] > W _result('NetConnection.Connect.Success'). - server_session.go:432
2025/07/03 08:16:37.302857 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=releaseStream, header={Csid:3 MsgLen:40 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=25, wpos=40, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:16:37.343585 [22;34mDEBUG [0m[RTMPPUBSUB2] read command message, ignore it. cmd=FCPublish, header={Csid:3 MsgLen:36 MsgTypeId:20 MsgStreamId:0 TimestampAbs:0}, b=len(core)=128, rpos=21, wpos=36, hex=00000000  05 02 00 0b 74 65 73 74  2d 73 74 72 65 61 6d     |....test-stream|
 - server_session.go:366
2025/07/03 08:16:37.343666 [22;36m INFO [0m[RTMPPUBSUB2] < R createStream(). - server_session.go:444
2025/07/03 08:16:37.343696 [22;36m INFO [0m[RTMPPUBSUB2] > W _result(). - server_session.go:445
2025/07/03 08:16:37.343882 [22;34mDEBUG [0m[RTMPPUBSUB2] pubType=live - server_session.go:474
2025/07/03 08:16:37.343904 [22;36m INFO [0m[RTMPPUBSUB2] < R publish('test-stream') - server_session.go:475
2025/07/03 08:16:37.343924 [22;36m INFO [0m[RTMPPUBSUB2] > W onStatus('NetStream.Publish.Start'). - server_session.go:477
2025/07/03 08:16:37.344038 [22;34mDEBUG [0m[GROUP1] [RTMPPUBSUB2] add rtmp pub session into group. - group__in.go:59
2025/07/03 08:16:37.344084 [22;34mDEBUG [0m[RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer - rtmp2mpegts.go:117
2025/07/03 08:16:37.344104 [22;34mDEBUG [0m[GROUP1] [RTMP2MPEGTS1] NewRtmp2MpegtsRemuxer in group. - group__in.go:357
2025/07/03 08:16:37.344130 [22;36m INFO [0m[HLSMUXER1] lifecycle new hls muxer. muxer=0xc0003e30e0, streamName=test-stream - muxer.go:116
2025/07/03 08:16:37.344148 [22;36m INFO [0m[HLSMUXER1] start hls muxer. - muxer.go:121
2025/07/03 08:16:37.344330 [22;34mDEBUG [0m[GROUP1] metadata. err=<nil>, len=20, value=duration: 0
fileSize: 0
width: 1920
height: 1080
videocodecid: 7
videodatarate: 2500
framerate: 60
audiocodecid: 10
audiodatarate: 160
audiosamplerate: 48000
audiosamplesize: 16
audiochannels: 2
stereo: true
2.1: false
3.1: false
4.0: false
4.1: false
5.1: false
7.1: false
encoder: obs-output module (libobs version 27.2.3+dfsg1-1)
 - group__core_streaming.go:190
2025/07/03 08:16:37.344404 [22;34mDEBUG [0m[GROUP1] cache rtmp metadata. size:423 - gop_cache.go:93
2025/07/03 08:16:37.990804 [22;34mDEBUG [0m[GROUP1] cache rtmp aac seq header. size:19 - gop_cache.go:109
2025/07/03 08:16:37.990894 [22;34mDEBUG [0m[GROUP1] cache rtmp video seq header. size:62 - gop_cache.go:115
2025/07/03 08:16:37.990933 [22;34mDEBUG [0msps={ProfileIdc:100 ConstraintSet0Flag:0 ConstraintSet1Flag:0 ConstraintSet2Flag:0 LevelIdc:42 SpsId:0 ChromaFormatIdc:1 ResidualColorTransformFlag:0 BitDepthLuma:8 BitDepthChroma:8 TransFormBypass:0 Log2MaxFrameNumMinus4:0 PicOrderCntType:0 Log2MaxPicOrderCntLsb:6 NumRefFrames:4 GapsInFrameNumValueAllowedFlag:0 PicWidthInMbsMinusOne:119 PicHeightInMapUnitsMinusOne:67 FrameMbsOnlyFlag:1 MbAdaptiveFrameFieldFlag:0 Direct8X8InferenceFlag:1 FrameCroppingFlag:1 FrameCropLeftOffset:0 FrameCropRightOffset:0 FrameCropTopOffset:0 FrameCropBottomOffset:4 SarNum:1 SarDen:1} - beta.go:41
2025/07/03 08:16:40.790264 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:42.090211 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:46.040566 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=2502152. - server_session.go:272
2025/07/03 08:16:48.339822 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:50.390402 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:52.435038 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:16:53.988616 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=5002959. - server_session.go:272
2025/07/03 08:17:01.956688 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=7503598. - server_session.go:272
2025/07/03 08:17:09.890104 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=10004137. - server_session.go:272
2025/07/03 08:17:13.239841 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:15.840180 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:17.841924 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=12504692. - server_session.go:272
2025/07/03 08:17:17.884498 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:21.239927 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.390069 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.540228 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.690365 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:22.839810 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.540438 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.690679 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.840411 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:23.990581 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:24.140640 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:24.290572 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:24.440150 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.140020 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.289454 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.440376 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.589504 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:25.633627 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=15036182. - server_session.go:272
2025/07/03 08:17:26.739875 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:26.889713 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.038691 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.187794 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.336833 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:27.634788 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.336983 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.489798 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.640050 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.790258 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:28.940056 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:29.089848 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:29.240103 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:30.389981 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:30.540846 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:30.840866 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.541947 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.690438 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.840022 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:31.989752 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:32.139720 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:32.289997 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:32.440089 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.139929 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.289884 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.439674 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.590094 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.740186 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:33.849097 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=17537756. - server_session.go:272
2025/07/03 08:17:33.890710 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:34.890199 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.039799 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.189090 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.338585 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.487416 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:35.636293 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.339271 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.488267 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.637661 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.786935 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:36.936021 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:37.085127 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:37.233984 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:37.939922 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:38.090195 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:38.840258 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.539757 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.690215 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.840058 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:39.990302 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:40.141076 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:40.290096 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:40.440182 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.140188 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.289971 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.440371 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.589744 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.739969 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:41.806852 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=20038613. - server_session.go:272
2025/07/03 08:17:41.889766 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:42.039739 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:42.890160 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.040519 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.190459 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.340428 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.490111 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:43.639953 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.340231 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.490308 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.640346 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.790021 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:44.940644 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:45.089609 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:45.238623 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:45.939982 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:46.090697 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:46.239960 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:46.388915 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:47.537460 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:49.740353 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:49.751511 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=22539359. - server_session.go:272
2025/07/03 08:17:50.740050 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:50.890549 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.040184 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.190275 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.340193 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.490011 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:51.640227 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.489676 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.639701 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.789816 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:52.939755 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:53.089949 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:55.989766 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:56.140070 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:56.290390 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.139944 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.289732 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.440185 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.589986 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.706601 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=25040109. - server_session.go:272
2025/07/03 08:17:57.755230 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:57.904591 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:58.053583 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:58.755075 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:59.202604 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:17:59.650274 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.353013 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.501880 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.651156 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.800609 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:00.949496 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:01.098413 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:01.247387 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:01.949970 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.098977 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.248083 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.396946 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.546062 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.695063 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:02.844111 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:03.695694 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:03.844866 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:03.994191 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:04.143363 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:04.292251 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:04.441416 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.144801 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.293985 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.443036 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.592537 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.673511 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=27541336. - server_session.go:272
2025/07/03 08:18:05.741650 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:05.890660 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:06.040525 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:06.743046 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:06.890916 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:07.040092 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.339886 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.490023 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.639795 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.789737 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:08.939730 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:09.089735 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:09.240048 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:09.956209 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.106777 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.255454 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.404710 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.553501 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.702328 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:10.851229 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.000995 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.149809 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.298984 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:12.447741 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.150655 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.299817 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.448580 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.597660 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.608587 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=30041834. - server_session.go:272
2025/07/03 08:18:13.746727 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:13.896050 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:14.052854 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:14.754983 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:14.904388 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:15.053315 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:15.202398 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:15.351114 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.351280 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.500208 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.649236 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.798060 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:16.947149 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:19.544356 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:19.991675 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:20.140433 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:20.290802 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:20.440345 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:21.143150 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:21.557053 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=32542599. - server_session.go:272
2025/07/03 08:18:21.589761 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:21.890481 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:22.039723 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:22.739546 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:22.890015 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.040409 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.189493 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.355249 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.504572 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:23.653506 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.355689 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.504725 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.653525 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.802864 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:24.951925 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:25.101356 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:25.250700 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:25.953153 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.102261 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.251401 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.400281 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.549131 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.698034 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:26.846885 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.550175 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.699137 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.847946 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:27.997032 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:28.146150 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:28.295157 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:28.444168 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.147271 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.296464 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.445604 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:29.509793 [22;36m INFO [0m[RTMPPUBSUB1] < R Acknowledgement. ignore. sequence number=35043502. - server_session.go:272
2025/07/03 08:18:30.745204 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:30.894183 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.043321 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.192287 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.341478 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.490749 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:31.640710 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.342015 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.491133 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.640528 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.790027 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:32.940596 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:33.090322 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:33.240378 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.390083 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.539938 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.689683 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:34.839967 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.540520 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.690139 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.840739 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:35.990811 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:36.155179 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
2025/07/03 08:18:36.304217 [22;34mDEBUG [0m[0xc000255740] Buffer::Grow. realloc, this round need=2048, copy=4096, cap=(4096 -> 6144) - buffer.go:150
