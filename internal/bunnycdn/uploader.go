package bunnycdn

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
)

type Uploader struct {
	endpoint string
	username string
	password string
	basePath string
}

func NewUploader(endpoint, username, password, basePath string) *Uploader {
	return &Uploader{
		endpoint: endpoint,
		username: username,
		password: password,
		basePath: basePath,
	}
}

func (u *Uploader) UploadFile(localPath, remotePath, cacheControl string) error {
	// Normalize paths
	remotePath = filepath.Join(u.basePath, remotePath)
	remoteDir := filepath.Dir(remotePath)

	// Create SSH configuration
	config := &ssh.ClientConfig{
		User: u.username,
		Auth: []ssh.AuthMethod{
			ssh.Password(u.password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// Connect to SFTP server
	conn, err := ssh.Dial("tcp", u.endpoint+":22", config)
	if err != nil {
		return fmt.Errorf("failed to dial: %w", err)
	}
	defer conn.Close()

	client, err := sftp.NewClient(conn)
	if err != nil {
		return fmt.Errorf("failed to create SFTP client: %w", err)
	}
	defer client.Close()

	// Create remote directories
	if err := client.MkdirAll(remoteDir); err != nil {
		return fmt.Errorf("failed to create remote directory: %w", err)
	}

	// Open local file
	srcFile, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("failed to open local file: %v", err)
	}
	defer srcFile.Close()

	// Create remote file
	dstFile, err := client.Create(remotePath)
	if err != nil {
		return fmt.Errorf("failed to create remote file: %v", err)
	}
	defer dstFile.Close()

	// Copy file contents
	if _, err := dstFile.ReadFrom(srcFile); err != nil {
		return fmt.Errorf("failed to copy file: %v", err)
	}

	log.Printf("Uploaded to BunnyCDN: %s (Cache-Control: %s)", remotePath, cacheControl)
	return nil
}
