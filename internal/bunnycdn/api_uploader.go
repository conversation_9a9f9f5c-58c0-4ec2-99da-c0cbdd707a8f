package bunnycdn

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
)

type APIUploader struct {
	apiKey      string
	storageZone string
	basePath    string
	cdnURL      string
}

func NewAPIUploader(apiKey, storageZone, basePath, cdnURL string) *APIUploader {
	return &APIUploader{
		apiKey:      apiKey,
		storageZone: storageZone,
		basePath:    basePath,
		cdnURL:      cdnURL,
	}
}

func (u *APIUploader) UploadFile(localPath, remotePath, cacheControl string) error {
	// Construct full remote path
	fullPath := filepath.Join(u.basePath, remotePath)

	// Open local file
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Create request body
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("file", filepath.Base(fullPath))
	if err != nil {
		return fmt.Errorf("failed to create form file: %w", err)
	}

	_, err = io.Copy(part, file)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %w", err)
	}

	// Add cache control header if specified
	if cacheControl != "" {
		_ = writer.WriteField("CacheControl", cacheControl)
	}

	err = writer.Close()
	if err != nil {
		return fmt.Errorf("failed to close writer: %w", err)
	}

	// Create request
	url := fmt.Sprintf("https://storage.bunnycdn.com/%s/%s", u.storageZone, fullPath)
	req, err := http.NewRequest("PUT", url, body)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("AccessKey", u.apiKey)

	// Execute request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed (%d): %s", resp.StatusCode, string(body))
	}

	log.Printf("Uploaded via API: %s (Cache-Control: %s)", fullPath, cacheControl)
	return nil
}
