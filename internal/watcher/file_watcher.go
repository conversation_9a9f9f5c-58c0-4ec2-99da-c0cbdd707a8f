package watcher

import (
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/fsnotify/fsnotify"
)

type Uploader interface {
	UploadFile(localPath, remotePath, cacheControl string) error
}

type FileWatcher struct {
	dir      string
	uploader Uploader
	watcher  *fsnotify.Watcher
}

func NewFileWatcher(dir string, uploader Uploader) *FileWatcher {
	return &FileWatcher{
		dir:      dir,
		uploader: uploader,
	}
}

func (fw *FileWatcher) Start() error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return err
	}
	fw.watcher = watcher

	// Add base directory and all subdirectories
	err = filepath.Walk(fw.dir, func(path string, info os.FileInfo, err error) error {
		if info != nil && info.IsDir() {
			return watcher.Add(path)
		}
		return nil
	})
	if err != nil {
		return err
	}

	go fw.watchEvents()
	return nil
}

func (fw *FileWatcher) Stop() {
	if fw.watcher != nil {
		fw.watcher.Close()
	}
}

func (fw *FileWatcher) watchEvents() {
	debounce := make(map[string]time.Time)
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case event, ok := <-fw.watcher.Events:
			if !ok {
				return
			}
			if event.Op&(fsnotify.Create|fsnotify.Write) == 0 {
				continue
			}

			ext := filepath.Ext(event.Name)
			if ext != ".ts" && ext != ".m3u8" {
				continue
			}

			debounce[event.Name] = time.Now()

		case <-ticker.C:
			now := time.Now()
			for file, lastChange := range debounce {
				if now.Sub(lastChange) < 500*time.Millisecond {
					continue
				}

				relPath, err := filepath.Rel(fw.dir, file)
				if err != nil {
					log.Printf("Error getting relative path: %v", err)
					continue
				}

				cacheControl := "public, max-age=31536000" // 1 year for TS
				if filepath.Ext(file) == ".m3u8" {
					cacheControl = "no-cache, max-age=0" // No cache for playlists
				}

				if err := fw.uploader.UploadFile(file, relPath, cacheControl); err != nil {
					log.Printf("Upload failed: %v", err)
				} else {
					log.Printf("Uploaded: %s", relPath)
				}

				delete(debounce, file)
			}

		case err, ok := <-fw.watcher.Errors:
			if !ok {
				return
			}
			log.Printf("Watcher error: %v", err)
		}
	}
}
