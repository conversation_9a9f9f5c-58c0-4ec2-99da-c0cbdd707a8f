package rtmp

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/q191201771/lal/pkg/base"
	"github.com/q191201771/lal/pkg/logic"
)

// LALServer wraps the LAL streaming server with OBS Studio compatibility
type LALServer struct {
	server     logic.ILalServer
	configPath string
	hlsPath    string
	port       int
}

// LALConfig represents the LAL server configuration
type LALConfig struct {
	ConfVersion string `json:"conf_version"`
	RTMP        struct {
		Enable bool   `json:"enable"`
		Addr   string `json:"addr"`
	} `json:"rtmp"`
	DefaultHTTP struct {
		HTTPListenAddr string `json:"http_listen_addr"`
	} `json:"default_http"`
	HLS struct {
		Enable              bool   `json:"enable"`
		URLPattern          string `json:"url_pattern"`
		OutPath             string `json:"out_path"`
		FragmentDurationMs  int    `json:"fragment_duration_ms"`
		FragmentNum         int    `json:"fragment_num"`
		DeleteThreshold     int    `json:"delete_threshold"`
		CleanupMode         int    `json:"cleanup_mode"`
		UseMemoryAsDiskFlag bool   `json:"use_memory_as_disk_flag"`
	} `json:"hls"`
	HTTPApi struct {
		Enable bool   `json:"enable"`
		Addr   string `json:"addr"`
	} `json:"http_api"`
	Log struct {
		Level               int    `json:"level"`
		Filename            string `json:"filename"`
		IsToStdout          bool   `json:"is_to_stdout"`
		IsRotateDaily       bool   `json:"is_rotate_daily"`
		ShortFileFlag       bool   `json:"short_file_flag"`
		TimestampFlag       bool   `json:"timestamp_flag"`
		TimestampWithMsFlag bool   `json:"timestamp_with_ms_flag"`
		LevelFlag           bool   `json:"level_flag"`
		AssertBehavior      int    `json:"assert_behavior"`
	} `json:"log"`
}

// NewLALServer creates a new LAL server instance with OBS-compatible configuration
func NewLALServer(rtmpPort int, hlsOutputPath string) (*LALServer, error) {
	// Create HLS output directory
	if err := os.MkdirAll(hlsOutputPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create HLS output directory: %v", err)
	}

	// Create logs directory
	logsDir := "./logs"
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create logs directory: %v", err)
	}

	// Create configuration
	config := &LALConfig{
		ConfVersion: "v0.4.1",
	}

	// RTMP configuration - optimized for OBS Studio
	config.RTMP.Enable = true
	config.RTMP.Addr = fmt.Sprintf(":%d", rtmpPort)

	// HTTP configuration (disabled since we use our own web server)
	config.DefaultHTTP.HTTPListenAddr = ":0" // Disable LAL's HTTP server

	// HLS configuration - optimized for ultra-low latency (500ms target)
	config.HLS.Enable = true
	config.HLS.URLPattern = "/hls/"
	config.HLS.OutPath = hlsOutputPath
	config.HLS.FragmentDurationMs = 500 // 500ms segments for ultra-low latency
	config.HLS.FragmentNum = 3          // Keep only 3 segments (1.5 seconds total)
	config.HLS.DeleteThreshold = 3      // Clean up old segments aggressively
	config.HLS.CleanupMode = 1          // Auto cleanup
	config.HLS.UseMemoryAsDiskFlag = false

	// HTTP API (disabled)
	config.HTTPApi.Enable = false
	config.HTTPApi.Addr = ":0"

	// Logging configuration
	config.Log.Level = 1 // Info level
	config.Log.Filename = "./logs/lal_rtmp.log"
	config.Log.IsToStdout = true
	config.Log.IsRotateDaily = true
	config.Log.ShortFileFlag = true
	config.Log.TimestampFlag = true
	config.Log.TimestampWithMsFlag = true
	config.Log.LevelFlag = true
	config.Log.AssertBehavior = 1

	// Write configuration to file
	configPath := "./conf/lal_rtmp.conf.json"
	if err := os.MkdirAll(filepath.Dir(configPath), 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %v", err)
	}

	configData, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal config: %v", err)
	}

	if err := os.WriteFile(configPath, configData, 0644); err != nil {
		return nil, fmt.Errorf("failed to write config file: %v", err)
	}

	log.Printf("LAL Server: Created configuration at %s", configPath)
	log.Printf("LAL Server: RTMP will listen on port %d", rtmpPort)
	log.Printf("LAL Server: HLS output directory: %s", hlsOutputPath)

	return &LALServer{
		configPath: configPath,
		hlsPath:    hlsOutputPath,
		port:       rtmpPort,
	}, nil
}

// Start starts the LAL server
func (s *LALServer) Start() error {
	log.Printf("LAL Server: Starting with config: %s", s.configPath)

	// Create LAL server instance
	s.server = logic.NewLalServer(func(option *logic.Option) {
		option.ConfFilename = s.configPath
	})

	// Start the server in a goroutine
	go func() {
		err := s.server.RunLoop()
		if err != nil {
			log.Printf("LAL Server: Server stopped with error: %v", err)
		} else {
			log.Printf("LAL Server: Server stopped gracefully")
		}
	}()

	// Give the server time to start
	time.Sleep(1 * time.Second)

	log.Printf("LAL Server: Started successfully")
	log.Printf("LAL Server: RTMP URL: rtmp://localhost:%d/live/<stream_key>", s.port)
	log.Printf("LAL Server: HLS files will be written to: %s", s.hlsPath)

	return nil
}

// Stop stops the LAL server
func (s *LALServer) Stop() error {
	if s.server != nil {
		log.Printf("LAL Server: Stopping...")
		s.server.Dispose()
		s.server = nil
		log.Printf("LAL Server: Stopped")
	}
	return nil
}

// GetHLSPath returns the HLS output path
func (s *LALServer) GetHLSPath() string {
	return s.hlsPath
}

// GetInfo returns server information
func (s *LALServer) GetInfo() map[string]interface{} {
	info := map[string]interface{}{
		"server_type":    "LAL",
		"version":        base.LalVersion,
		"hls_path":       s.hlsPath,
		"config_path":    s.configPath,
		"obs_compatible": true,
	}

	if s.server != nil {
		info["status"] = "running"
	} else {
		info["status"] = "stopped"
	}

	return info
}
