package rtmp

import (
	"fmt"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// MockHLSSegmenter implements HLSSegmenter for testing
type MockHLSSegmenter struct {
	baseDir   string
	started   bool
	streamKey string
	rtmpURL   string
	outputDir string
}

func NewMockHLSSegmenter(baseDir string) *MockHLSSegmenter {
	return &MockHLSSegmenter{
		baseDir: baseDir,
	}
}

func (m *MockHLSSegmenter) GetBaseDir() string {
	return m.baseDir
}

func (m *MockHLSSegmenter) StartSegmentation(streamKey, rtmpURL, outputDir string) (*exec.Cmd, error) {
	m.started = true
	m.streamKey = streamKey
	m.rtmpURL = rtmpURL
	m.outputDir = outputDir

	// Create a dummy command that just sleeps
	cmd := exec.Command("sleep", "3600")
	err := cmd.Start()
	return cmd, err
}

func TestNewServer(t *testing.T) {
	tempDir := t.TempDir()
	manager := NewMockHLSSegmenter(tempDir)

	server := NewServer(1935, manager)

	if server == nil {
		t.Fatal("NewServer returned nil")
	}

	if server.port != 1935 {
		t.Errorf("Expected port 1935, got %d", server.port)
	}

	if server.manager != manager {
		t.Error("Manager not set correctly")
	}

	if server.server == nil {
		t.Error("RTMP server not initialized")
	}
}

func TestServerStartStop(t *testing.T) {
	tempDir := t.TempDir()
	manager := NewMockHLSSegmenter(tempDir)

	// Use a random available port for testing
	server := NewServer(0, manager)

	// Start server in goroutine
	errChan := make(chan error, 1)
	go func() {
		// Get an available port
		listener, err := net.Listen("tcp", ":0")
		if err != nil {
			errChan <- err
			return
		}
		port := listener.Addr().(*net.TCPAddr).Port
		listener.Close()

		server.port = port
		errChan <- server.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Test that server is listening
	conn, err := net.Dial("tcp", fmt.Sprintf("localhost:%d", server.port))
	if err != nil {
		t.Fatalf("Could not connect to server: %v", err)
	}
	conn.Close()

	// Stop server
	server.Stop()

	// Check if server stopped (should get connection refused)
	time.Sleep(100 * time.Millisecond)
	_, err = net.Dial("tcp", fmt.Sprintf("localhost:%d", server.port))
	if err == nil {
		t.Error("Server should have stopped but connection succeeded")
	}
}

func TestRTMPStreamProcessing(t *testing.T) {
	tempDir := t.TempDir()
	manager := NewMockHLSSegmenter(tempDir)

	// Test with ffmpeg if available
	if !isFFmpegAvailable() {
		t.Skip("FFmpeg not available, skipping integration test")
	}

	// Get an available port
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		t.Fatalf("Could not get available port: %v", err)
	}
	port := listener.Addr().(*net.TCPAddr).Port
	listener.Close()

	server := NewServer(port, manager)

	// Start server
	go func() {
		server.Start()
	}()

	// Give server time to start
	time.Sleep(200 * time.Millisecond)
	defer server.Stop()

	// Test streaming with ffmpeg
	streamKey := "test-stream"
	rtmpURL := fmt.Sprintf("rtmp://localhost:%d/live/%s", port, streamKey)

	// Create a very short test stream
	cmd := exec.Command("ffmpeg",
		"-f", "lavfi",
		"-i", "testsrc=duration=1:size=320x240:rate=30",
		"-f", "lavfi",
		"-i", "sine=frequency=1000:duration=1",
		"-c:v", "libx264",
		"-preset", "ultrafast",
		"-c:a", "aac",
		"-f", "flv",
		rtmpURL,
	)

	// Start the command and wait with timeout
	done := make(chan error, 1)
	go func() {
		done <- cmd.Run()
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Logf("FFmpeg command failed (this might be expected): %v", err)
		}
	case <-time.After(5 * time.Second):
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		t.Log("FFmpeg command timed out and was killed")
	}

	// Give some time for processing
	time.Sleep(200 * time.Millisecond)

	// Check if HLS segmentation was started
	if !manager.started {
		t.Error("HLS segmentation was not started")
	}

	expectedStreamKey := "live/" + streamKey
	if manager.streamKey != expectedStreamKey {
		t.Errorf("Expected stream key %s, got %s", expectedStreamKey, manager.streamKey)
	}

	expectedOutputDir := filepath.Join(tempDir, "live", streamKey)
	if manager.outputDir != expectedOutputDir {
		t.Errorf("Expected output dir %s, got %s", expectedOutputDir, manager.outputDir)
	}
}

func TestStreamKeyExtraction(t *testing.T) {
	tests := []struct {
		path     string
		expected string
		valid    bool
	}{
		{"/live/test-stream", "live/test-stream", true},
		{"/live/my-stream", "live/my-stream", true},
		{"/", "", false},
		{"", "", false},
		{"/live", "live", true},
	}

	for _, tt := range tests {
		t.Run(tt.path, func(t *testing.T) {
			streamKey := strings.TrimPrefix(tt.path, "/")

			if tt.valid && streamKey == "" {
				t.Errorf("Expected valid stream key for path %s", tt.path)
			}

			if !tt.valid && streamKey != "" && tt.expected == "" {
				// This test case expects empty result
				return
			}

			if streamKey != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, streamKey)
			}
		})
	}
}

func TestHLSDirectoryCreation(t *testing.T) {
	tempDir := t.TempDir()

	streamKey := "live/test-stream"
	expectedDir := filepath.Join(tempDir, streamKey)

	// This simulates what happens in handleConn
	err := os.MkdirAll(expectedDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create directory: %v", err)
	}

	// Check if directory exists
	if _, err := os.Stat(expectedDir); os.IsNotExist(err) {
		t.Errorf("Directory %s was not created", expectedDir)
	}

	// Check permissions
	info, err := os.Stat(expectedDir)
	if err != nil {
		t.Fatalf("Could not stat directory: %v", err)
	}

	if info.Mode().Perm() != 0755 {
		t.Errorf("Expected permissions 0755, got %v", info.Mode().Perm())
	}
}

// Helper function to check if FFmpeg is available
func isFFmpegAvailable() bool {
	_, err := exec.LookPath("ffmpeg")
	return err == nil
}

// Benchmark test for server performance
func BenchmarkServerConnection(b *testing.B) {
	tempDir := b.TempDir()
	manager := NewMockHLSSegmenter(tempDir)

	// Get an available port
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		b.Fatalf("Could not get available port: %v", err)
	}
	port := listener.Addr().(*net.TCPAddr).Port
	listener.Close()

	server := NewServer(port, manager)

	// Start server
	go func() {
		server.Start()
	}()

	time.Sleep(100 * time.Millisecond)
	defer server.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		conn, err := net.Dial("tcp", fmt.Sprintf("localhost:%d", port))
		if err != nil {
			b.Fatalf("Could not connect: %v", err)
		}
		conn.Close()
	}
}
