package rtmp

import (
	"log"
	"net"
	"time"
)

// OBSCompatibilityLayer provides workarounds for OBS Studio compatibility issues
type OBSCompatibilityLayer struct {
	connections map[string]*ConnectionInfo
}

type ConnectionInfo struct {
	RemoteAddr  string
	ConnectedAt time.Time
	StreamKey   string
}

func NewOBSCompatibilityLayer() *OBSCompatibilityLayer {
	return &OBSCompatibilityLayer{
		connections: make(map[string]*ConnectionInfo),
	}
}

// RegisterConnection tracks OBS connections for compatibility fixes
func (o *OBSCompatibilityLayer) RegisterConnection(remoteAddr, streamKey string) {
	o.connections[remoteAddr] = &ConnectionInfo{
		RemoteAddr:  remoteAddr,
		ConnectedAt: time.Now(),
		StreamKey:   streamKey,
	}
	log.Printf("OBS Compatibility: Registered connection from %s for stream %s", remoteAddr, streamKey)
}

// UnregisterConnection removes tracking for a connection
func (o *OBSCompatibilityLayer) UnregisterConnection(remoteAddr string) {
	if info, exists := o.connections[remoteAddr]; exists {
		duration := time.Since(info.ConnectedAt)
		log.Printf("OBS Compatibility: Unregistered connection from %s (duration: %v)", remoteAddr, duration)
		delete(o.connections, remoteAddr)
	}
}

// IsOBSConnection attempts to detect if a connection is from OBS Studio
func (o *OBSCompatibilityLayer) IsOBSConnection(nc net.Conn) bool {
	// OBS typically connects from IPv4 localhost
	remoteAddr := nc.RemoteAddr().String()
	
	// Simple heuristic: OBS usually connects from 127.0.0.1
	// while FFmpeg often uses IPv6 [::1]
	if len(remoteAddr) > 9 && remoteAddr[:9] == "127.0.0.1" {
		return true
	}
	
	return false
}

// SendOBSAcknowledgment attempts to send a compatibility response to OBS
func (o *OBSCompatibilityLayer) SendOBSAcknowledgment(nc net.Conn, streamKey string) {
	if !o.IsOBSConnection(nc) {
		return
	}

	remoteAddr := nc.RemoteAddr().String()
	log.Printf("OBS Compatibility: Attempting to send acknowledgment to %s for stream %s", remoteAddr, streamKey)

	// Register the connection
	o.RegisterConnection(remoteAddr, streamKey)

	// Try to send a simple acknowledgment
	// Note: This is a basic attempt - the joy5 library handles most RTMP protocol details
	go func() {
		// Give the RTMP handshake time to complete
		time.Sleep(100 * time.Millisecond)

		// Log that we're attempting to help OBS
		log.Printf("OBS Compatibility: Connection from %s should now be ready for streaming", remoteAddr)

		// Try to send a simple TCP keepalive to help OBS recognize the connection is active
		// This won't send the proper RTMP NetStream.Publish.Start message, but might help
		o.sendTCPKeepalive(nc, remoteAddr)
	}()
}

// sendTCPKeepalive sends a simple TCP keepalive to help maintain the connection
func (o *OBSCompatibilityLayer) sendTCPKeepalive(nc net.Conn, remoteAddr string) {
	// Set a short deadline for the keepalive attempt
	nc.SetWriteDeadline(time.Now().Add(1 * time.Second))
	defer nc.SetWriteDeadline(time.Time{}) // Reset deadline

	// We can't send raw RTMP messages since joy5 manages the protocol
	// But we can log that we're trying to help OBS
	log.Printf("OBS Compatibility: Maintaining connection for %s", remoteAddr)

	// The connection is already established and working
	// OBS should eventually recognize that data is flowing
}

// GetConnectionInfo returns information about a tracked connection
func (o *OBSCompatibilityLayer) GetConnectionInfo(remoteAddr string) *ConnectionInfo {
	return o.connections[remoteAddr]
}

// ListActiveConnections returns all currently tracked connections
func (o *OBSCompatibilityLayer) ListActiveConnections() map[string]*ConnectionInfo {
	result := make(map[string]*ConnectionInfo)
	for k, v := range o.connections {
		result[k] = v
	}
	return result
}
