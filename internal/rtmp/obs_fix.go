package rtmp

import (
	"log"
	"net"
	"time"
)

// OBSCompatibilityLayer provides workarounds for OBS Studio compatibility issues
type OBSCompatibilityLayer struct {
	connections map[string]*ConnectionInfo
}

type ConnectionInfo struct {
	RemoteAddr  string
	ConnectedAt time.Time
	StreamKey   string
}

func NewOBSCompatibilityLayer() *OBSCompatibilityLayer {
	return &OBSCompatibilityLayer{
		connections: make(map[string]*ConnectionInfo),
	}
}

// RegisterConnection tracks OBS connections for compatibility fixes
func (o *OBSCompatibilityLayer) RegisterConnection(remoteAddr, streamKey string) {
	o.connections[remoteAddr] = &ConnectionInfo{
		RemoteAddr:  remoteAddr,
		ConnectedAt: time.Now(),
		StreamKey:   streamKey,
	}
	log.Printf("OBS Compatibility: Registered connection from %s for stream %s", remoteAddr, streamKey)
}

// UnregisterConnection removes tracking for a connection
func (o *OBSCompatibilityLayer) UnregisterConnection(remoteAddr string) {
	if info, exists := o.connections[remoteAddr]; exists {
		duration := time.Since(info.ConnectedAt)
		log.Printf("OBS Compatibility: Unregistered connection from %s (duration: %v)", remoteAddr, duration)
		delete(o.connections, remoteAddr)
	}
}

// IsOBSConnection attempts to detect if a connection is from OBS Studio
func (o *OBSCompatibilityLayer) IsOBSConnection(nc net.Conn) bool {
	// OBS typically connects from IPv4 localhost
	remoteAddr := nc.RemoteAddr().String()
	
	// Simple heuristic: OBS usually connects from 127.0.0.1
	// while FFmpeg often uses IPv6 [::1]
	if len(remoteAddr) > 9 && remoteAddr[:9] == "127.0.0.1" {
		return true
	}
	
	return false
}

// SendOBSAcknowledgment attempts to send a compatibility response to OBS
func (o *OBSCompatibilityLayer) SendOBSAcknowledgment(nc net.Conn, streamKey string) {
	if !o.IsOBSConnection(nc) {
		return
	}
	
	remoteAddr := nc.RemoteAddr().String()
	log.Printf("OBS Compatibility: Attempting to send acknowledgment to %s for stream %s", remoteAddr, streamKey)
	
	// Register the connection
	o.RegisterConnection(remoteAddr, streamKey)
	
	// Try to send a simple acknowledgment
	// Note: This is a basic attempt - the joy5 library handles most RTMP protocol details
	go func() {
		// Give the RTMP handshake time to complete
		time.Sleep(100 * time.Millisecond)
		
		// Log that we're attempting to help OBS
		log.Printf("OBS Compatibility: Connection from %s should now be ready for streaming", remoteAddr)
		
		// The actual RTMP acknowledgment would need to be sent through the RTMP protocol
		// Since joy5 doesn't expose this, we'll rely on the fact that the connection is working
		// and hope OBS eventually recognizes the successful stream processing
	}()
}

// GetConnectionInfo returns information about a tracked connection
func (o *OBSCompatibilityLayer) GetConnectionInfo(remoteAddr string) *ConnectionInfo {
	return o.connections[remoteAddr]
}

// ListActiveConnections returns all currently tracked connections
func (o *OBSCompatibilityLayer) ListActiveConnections() map[string]*ConnectionInfo {
	result := make(map[string]*ConnectionInfo)
	for k, v := range o.connections {
		result[k] = v
	}
	return result
}
