package rtmp

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

type HLSSegmenter interface {
	GetBaseDir() string
	StartSegmentation(streamKey, rtmpURL, outputDir string) (*exec.Cmd, error)
}

type Server struct {
	port      int
	lalServer *LALServer
	manager   HLSSegmenter
	hlsPath   string
}

func NewServer(port int, manager HLSSegmenter) *Server {
	// Use the HLS manager's base directory for LAL output
	hlsPath := filepath.Join(manager.GetBaseDir(), "lal_hls")

	s := &Server{
		port:    port,
		manager: manager,
		hlsPath: hlsPath,
	}

	return s
}

func (s *Server) Start() error {
	log.Printf("Starting LAL-based RTMP server on port %d", s.port)

	// Create LAL server instance
	lalServer, err := NewLALServer(s.port, s.hlsPath)
	if err != nil {
		return fmt.Errorf("failed to create LAL server: %v", err)
	}
	s.lalServer = lalServer

	// Start LAL server
	if err := s.lalServer.Start(); err != nil {
		return fmt.Errorf("failed to start LAL server: %v", err)
	}

	log.Printf("LAL RTMP server started successfully")
	log.Printf("OBS Studio can connect to: rtmp://localhost:%d/live/<stream_key>", s.port)
	log.Printf("HLS files will be output to: %s", s.hlsPath)

	// Start monitoring for new streams
	go s.monitorStreams()

	return nil
}

func (s *Server) Stop() {
	log.Printf("Stopping LAL RTMP server...")
	if s.lalServer != nil {
		s.lalServer.Stop()
		s.lalServer = nil
	}
	log.Printf("LAL RTMP server stopped")
}

// monitorStreams monitors the LAL HLS output directory for new streams
func (s *Server) monitorStreams() {
	log.Printf("Starting stream monitor for directory: %s", s.hlsPath)

	// Monitor the HLS output directory for new streams
	// LAL will create subdirectories for each stream key
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	knownStreams := make(map[string]bool)

	for {
		select {
		case <-ticker.C:
			// Check for new stream directories
			entries, err := os.ReadDir(s.hlsPath)
			if err != nil {
				// Directory might not exist yet
				continue
			}

			for _, entry := range entries {
				if entry.IsDir() {
					streamKey := entry.Name()
					if !knownStreams[streamKey] {
						log.Printf("New stream detected: %s", streamKey)
						knownStreams[streamKey] = true

						// Check if this stream has HLS files
						streamDir := filepath.Join(s.hlsPath, streamKey)
						go s.handleNewStream(streamKey, streamDir)
					}
				}
			}
		}
	}
}

// handleNewStream handles a newly detected stream
func (s *Server) handleNewStream(streamKey, streamDir string) {
	log.Printf("Handling new stream: %s in directory: %s", streamKey, streamDir)

	// Wait for HLS files to appear
	maxWait := 30 * time.Second
	start := time.Now()

	for time.Since(start) < maxWait {
		files, err := os.ReadDir(streamDir)
		if err != nil {
			time.Sleep(1 * time.Second)
			continue
		}

		// Look for .m3u8 playlist file
		hasPlaylist := false
		for _, file := range files {
			if strings.HasSuffix(file.Name(), ".m3u8") {
				hasPlaylist = true
				break
			}
		}

		if hasPlaylist {
			log.Printf("HLS playlist found for stream: %s", streamKey)

			// Create output directory in our HLS manager's base directory
			outputDir := filepath.Join(s.manager.GetBaseDir(), streamKey)
			if err := os.MkdirAll(outputDir, 0755); err != nil {
				log.Printf("Error creating output directory: %v", err)
				return
			}

			// Start our HLS segmentation (this will copy/process LAL's output)
			rtmpURL := fmt.Sprintf("rtmp://localhost:%d/live/%s", s.port, streamKey)
			cmd, err := s.manager.StartSegmentation(streamKey, rtmpURL, outputDir)
			if err != nil {
				log.Printf("Error starting HLS segmentation: %v", err)
				return
			}

			// Store the command for cleanup (optional - could track active streams)
			_ = cmd

			log.Printf("Stream processing started for: %s", streamKey)
			return
		}

		time.Sleep(1 * time.Second)
	}

	log.Printf("Timeout waiting for HLS files for stream: %s", streamKey)
}
