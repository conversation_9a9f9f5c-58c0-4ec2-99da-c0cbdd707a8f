package rtmp

import (
	"context"
	"io"
	"log"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/nareix/joy5/format/rtmp"
)

type HLSSegmenter interface {
	GetBaseDir() string
	StartSegmentation(streamKey, rtmpURL, outputDir string) (*exec.Cmd, error)
}

type Server struct {
	port     int
	server   *rtmp.Server
	manager  HLSSegmenter
	listener net.Listener
}

func NewServer(port int, manager HLSSegmenter) *Server {
	srv := &rtmp.Server{}
	s := &Server{
		port:    port,
		manager: manager,
		server:  srv,
	}
	srv.HandleConn = s.handleConn
	return s
}

func (s *Server) Start() error {
	listener, err := net.Listen("tcp", ":"+strconv.Itoa(s.port))
	if err != nil {
		return err
	}
	s.listener = listener
	log.Printf("RTMP server listening on :%d", s.port)

	for {
		conn, err := listener.Accept()
		if err != nil {
			return err
		}
		go s.server.HandleNetConn(conn)
	}
}

func (s *Server) Stop() {
	if s.listener != nil {
		s.listener.Close()
	}
}

func (s *Server) handleConn(conn *rtmp.Conn, nc net.Conn) {
	// Wait for the connection to be established
	nc.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, err := conn.ReadPacket()
	nc.SetReadDeadline(time.Time{})
	if err != nil {
		log.Printf("Error reading packet: %v", err)
		return
	}

	// Only handle publish connections
	if !conn.Publishing {
		return
	}

	streamKey := strings.TrimPrefix(conn.URL.Path, "/")
	if streamKey == "" {
		log.Println("Invalid stream key")
		return
	}

	log.Printf("New stream: %s", streamKey)

	// Create output directory for HLS segments
	outputDir := filepath.Join(s.manager.GetBaseDir(), streamKey)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Printf("Error creating output directory: %v", err)
		return
	}

	// Start FFmpeg process
	rtmpURL := "rtmp://localhost:" + strconv.Itoa(s.port) + "/" + streamKey
	cmd, err := s.manager.StartSegmentation(streamKey, rtmpURL, outputDir)
	if err != nil {
		log.Printf("Error starting FFmpeg: %v", err)
		return
	}
	defer cmd.Process.Kill()

	// Wait for stream to end
	ctx, cancel := context.WithCancel(context.Background())
	go func() {
		io.Copy(io.Discard, nc)
		cancel()
	}()

	<-ctx.Done()
	log.Printf("Stream ended: %s", streamKey)
}
