package rtmp

import (
	"log"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/nareix/joy5/format/rtmp"
)

type HLSSegmenter interface {
	GetBaseDir() string
	StartSegmentation(streamKey, rtmpURL, outputDir string) (*exec.Cmd, error)
}

type Server struct {
	port     int
	server   *rtmp.Server
	manager  HLSSegmenter
	listener net.Listener
}

func NewServer(port int, manager HLSSegmenter) *Server {
	srv := &rtmp.Server{}
	s := &Server{
		port:    port,
		manager: manager,
		server:  srv,
	}
	srv.HandleConn = s.handleConn

	return s
}

func (s *Server) Start() error {
	listener, err := net.Listen("tcp", ":"+strconv.Itoa(s.port))
	if err != nil {
		return err
	}
	s.listener = listener
	log.Printf("RTMP server listening on :%d", s.port)

	for {
		conn, err := listener.Accept()
		if err != nil {
			return err
		}
		go s.server.HandleNetConn(conn)
	}
}

func (s *Server) Stop() {
	if s.listener != nil {
		s.listener.Close()
	}
}

func (s *Server) handleConn(conn *rtmp.Conn, nc net.Conn) {
	log.Printf("Connection received from %s, URL: %s, Publishing: %v", nc.RemoteAddr(), conn.URL.Path, conn.Publishing)

	// The connection is already established and handshake is done by joy5
	// Check if this is a publishing connection
	if !conn.Publishing {
		log.Printf("Non-publishing connection from %s, URL: %s", nc.RemoteAddr(), conn.URL.Path)
		// This is expected - FFmpeg will also connect to read the stream
		return
	}

	streamKey := strings.TrimPrefix(conn.URL.Path, "/")
	if streamKey == "" {
		log.Println("Invalid stream key")
		return
	}

	log.Printf("New publishing stream: %s from %s", streamKey, nc.RemoteAddr())

	// Send publish response to acknowledge the stream
	// This is important for OBS to know the connection was successful
	log.Printf("Sending publish acknowledgment for stream: %s", streamKey)

	// Create output directory for HLS segments
	outputDir := filepath.Join(s.manager.GetBaseDir(), streamKey)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Printf("Error creating output directory: %v", err)
		return
	}

	// Start FFmpeg process
	rtmpURL := "rtmp://localhost:" + strconv.Itoa(s.port) + "/" + streamKey
	cmd, err := s.manager.StartSegmentation(streamKey, rtmpURL, outputDir)
	if err != nil {
		log.Printf("Error starting FFmpeg: %v", err)
		return
	}
	defer cmd.Process.Kill()

	// Wait for stream to end using the connection's close notification
	log.Printf("Waiting for stream to end: %s", streamKey)
	<-conn.CloseNotify()
	log.Printf("Stream ended: %s", streamKey)
}
