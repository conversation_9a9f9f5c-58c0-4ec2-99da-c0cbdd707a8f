package hls

import (
	"log"
	"os"
	"os/exec"
	"path/filepath"
)

type Manager struct {
	baseDir string
}

func NewManager(baseDir string) *Manager {
	if err := os.MkdirAll(baseDir, 0755); err != nil {
		log.Fatalf("Failed to create HLS directory: %v", err)
	}
	return &Manager{baseDir: baseDir}
}

func (m *Manager) GetBaseDir() string {
	return m.baseDir
}

func (m *Manager) StartSegmentation(streamKey, rtmpURL, outputDir string) (*exec.Cmd, error) {
	outputPath := filepath.Join(outputDir, "stream.m3u8")

	cmd := exec.Command("ffmpeg",
		"-i", rtmpURL,
		"-c:v", "copy",
		"-c:a", "copy",
		"-f", "hls",
		"-hls_time", "0.5",
		"-hls_list_size", "5",
		"-hls_flags", "delete_segments",
		outputPath,
	)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Start(); err != nil {
		return nil, err
	}

	return cmd, nil
}
